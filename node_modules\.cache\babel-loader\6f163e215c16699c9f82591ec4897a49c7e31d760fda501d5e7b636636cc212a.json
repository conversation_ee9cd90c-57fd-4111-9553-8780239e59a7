{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\WarehousePurchases.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { formatCurrency, formatDate } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WarehousePurchases = () => {\n  _s();\n  const [purchases, setPurchases] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    itemName: '',\n    quantity: '',\n    unitPrice: '',\n    supplier: '',\n    invoiceNumber: '',\n    notes: ''\n  });\n  useEffect(() => {\n    // بيانات تجريبية محدثة\n    setPurchases([{\n      id: 1,\n      date: '2024-01-15',\n      itemName: 'مواد خام - حديد مقاوم للصدأ',\n      quantity: 100,\n      unitPrice: 85,\n      totalPrice: 8500,\n      supplier: 'شركة الحديد المتحدة',\n      invoiceNumber: 'INV-001',\n      notes: 'جودة عالية - مقاوم للصدأ',\n      category: 'مواد خام',\n      unit: 'كيلو'\n    }, {\n      id: 2,\n      date: '2024-01-14',\n      itemName: 'أدوات تصنيع متقدمة',\n      quantity: 25,\n      unitPrice: 120,\n      totalPrice: 3000,\n      supplier: 'مؤسسة الأدوات الصناعية',\n      invoiceNumber: 'INV-002',\n      notes: 'أدوات عالية الدقة',\n      category: 'أدوات',\n      unit: 'قطعة'\n    }, {\n      id: 3,\n      date: '2024-01-13',\n      itemName: 'مواد كيميائية للطلاء',\n      quantity: 50,\n      unitPrice: 45,\n      totalPrice: 2250,\n      supplier: 'شركة الكيماويات المتخصصة',\n      invoiceNumber: 'INV-003',\n      notes: 'مواد صديقة للبيئة',\n      category: 'كيماويات',\n      unit: 'لتر'\n    }, {\n      id: 4,\n      date: '2024-01-12',\n      itemName: 'قطع غيار آلات',\n      quantity: 15,\n      unitPrice: 200,\n      totalPrice: 3000,\n      supplier: 'مركز قطع الغيار',\n      invoiceNumber: 'INV-004',\n      notes: 'قطع أصلية مضمونة',\n      category: 'قطع غيار',\n      unit: 'قطعة'\n    }, {\n      id: 5,\n      date: '2024-01-11',\n      itemName: 'مواد تعبئة وتغليف',\n      quantity: 200,\n      unitPrice: 12,\n      totalPrice: 2400,\n      supplier: 'شركة التعبئة الحديثة',\n      invoiceNumber: 'INV-005',\n      notes: 'مواد قابلة للتدوير',\n      category: 'تعبئة',\n      unit: 'وحدة'\n    }]);\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    const newPurchase = {\n      id: purchases.length + 1,\n      ...formData,\n      quantity: parseInt(formData.quantity),\n      unitPrice: parseFloat(formData.unitPrice),\n      totalPrice: parseInt(formData.quantity) * parseFloat(formData.unitPrice)\n    };\n    setPurchases(prev => [newPurchase, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      itemName: '',\n      quantity: '',\n      unitPrice: '',\n      supplier: '',\n      invoiceNumber: '',\n      notes: ''\n    });\n    setShowForm(false);\n  };\n  const getTotalValue = () => {\n    return purchases.reduce((sum, purchase) => sum + purchase.totalPrice, 0);\n  };\n  const getTotalItems = () => {\n    return purchases.reduce((sum, purchase) => sum + purchase.quantity, 0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"warehouse-purchases\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#1e3a8a'\n          },\n          children: formatCurrency(getTotalValue())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#ffa500'\n          },\n          children: getTotalItems()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#ea580c'\n          },\n          children: purchases.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#1e40af'\n          },\n          children: new Set(purchases.map(p => p.supplier)).size\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => setShowForm(!showForm),\n          children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A \\u062C\\u062F\\u064A\\u062F\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), showForm && /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        style: {\n          marginBottom: '20px',\n          padding: '20px',\n          backgroundColor: '#f8f9fa',\n          borderRadius: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              name: \"date\",\n              value: formData.date,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"invoiceNumber\",\n              value: formData.invoiceNumber,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0635\\u0646\\u0641\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"itemName\",\n            value: formData.itemName,\n            onChange: handleInputChange,\n            className: \"form-input\",\n            placeholder: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0635\\u0646\\u0641 \\u0623\\u0648 \\u0627\\u0644\\u0645\\u0627\\u062F\\u0629\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"quantity\",\n              value: formData.quantity,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"unitPrice\",\n              value: formData.unitPrice,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\",\n              step: \"0.01\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"supplier\",\n            value: formData.supplier,\n            onChange: handleInputChange,\n            className: \"form-input\",\n            placeholder: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            name: \"notes\",\n            value: formData.notes,\n            onChange: handleInputChange,\n            className: \"form-input\",\n            placeholder: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629\",\n            rows: \"3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this), formData.quantity && formData.unitPrice && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-info\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u0644\\u063A: \", formatCurrency(formData.quantity * formData.unitPrice)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: \"\\uD83D\\uDCBE \\u062D\\u0641\\u0638 \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn btn-secondary\",\n            onClick: () => setShowForm(false),\n            children: \"\\u274C \\u0625\\u0644\\u063A\\u0627\\u0621\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0635\\u0646\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: purchases.map(purchase => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(purchase.date).toLocaleDateString('ar-SA')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: purchase.itemName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [purchase.quantity, \" \", purchase.unit]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatCurrency(purchase.unitPrice)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  fontWeight: 'bold',\n                  color: '#1e3a8a'\n                },\n                children: formatCurrency(purchase.totalPrice)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: purchase.supplier\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: purchase.invoiceNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '5px 10px',\n                    fontSize: '12px'\n                  },\n                  children: \"\\u270F\\uFE0F \\u062A\\u0639\\u062F\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this)]\n            }, purchase.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n};\n_s(WarehousePurchases, \"9CsT2i5s2pYPiZ+af+pPz+tztBQ=\");\n_c = WarehousePurchases;\nexport default WarehousePurchases;\nvar _c;\n$RefreshReg$(_c, \"WarehousePurchases\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "formatCurrency", "formatDate", "jsxDEV", "_jsxDEV", "WarehousePurchases", "_s", "purchases", "setPurchases", "showForm", "setShowForm", "formData", "setFormData", "date", "Date", "toISOString", "split", "itemName", "quantity", "unitPrice", "supplier", "invoiceNumber", "notes", "id", "totalPrice", "category", "unit", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "newPurchase", "length", "parseInt", "parseFloat", "getTotalValue", "reduce", "sum", "purchase", "getTotalItems", "className", "children", "style", "marginBottom", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Set", "map", "p", "size", "onClick", "onSubmit", "padding", "backgroundColor", "borderRadius", "type", "onChange", "required", "placeholder", "step", "rows", "display", "gap", "toLocaleDateString", "fontWeight", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/WarehousePurchases.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { formatCurrency, formatDate } from '../utils/currency';\n\nconst WarehousePurchases = () => {\n  const [purchases, setPurchases] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    itemName: '',\n    quantity: '',\n    unitPrice: '',\n    supplier: '',\n    invoiceNumber: '',\n    notes: ''\n  });\n\n  useEffect(() => {\n    // بيانات تجريبية محدثة\n    setPurchases([\n      {\n        id: 1,\n        date: '2024-01-15',\n        itemName: 'مواد خام - حديد مقاوم للصدأ',\n        quantity: 100,\n        unitPrice: 85,\n        totalPrice: 8500,\n        supplier: 'شركة الحديد المتحدة',\n        invoiceNumber: 'INV-001',\n        notes: 'جودة عالية - مقاوم للصدأ',\n        category: 'مواد خام',\n        unit: 'كيلو'\n      },\n      {\n        id: 2,\n        date: '2024-01-14',\n        itemName: 'أدوات تصنيع متقدمة',\n        quantity: 25,\n        unitPrice: 120,\n        totalPrice: 3000,\n        supplier: 'مؤسسة الأدوات الصناعية',\n        invoiceNumber: 'INV-002',\n        notes: 'أدوات عالية الدقة',\n        category: 'أدوات',\n        unit: 'قطعة'\n      },\n      {\n        id: 3,\n        date: '2024-01-13',\n        itemName: 'مواد كيميائية للطلاء',\n        quantity: 50,\n        unitPrice: 45,\n        totalPrice: 2250,\n        supplier: 'شركة الكيماويات المتخصصة',\n        invoiceNumber: 'INV-003',\n        notes: 'مواد صديقة للبيئة',\n        category: 'كيماويات',\n        unit: 'لتر'\n      },\n      {\n        id: 4,\n        date: '2024-01-12',\n        itemName: 'قطع غيار آلات',\n        quantity: 15,\n        unitPrice: 200,\n        totalPrice: 3000,\n        supplier: 'مركز قطع الغيار',\n        invoiceNumber: 'INV-004',\n        notes: 'قطع أصلية مضمونة',\n        category: 'قطع غيار',\n        unit: 'قطعة'\n      },\n      {\n        id: 5,\n        date: '2024-01-11',\n        itemName: 'مواد تعبئة وتغليف',\n        quantity: 200,\n        unitPrice: 12,\n        totalPrice: 2400,\n        supplier: 'شركة التعبئة الحديثة',\n        invoiceNumber: 'INV-005',\n        notes: 'مواد قابلة للتدوير',\n        category: 'تعبئة',\n        unit: 'وحدة'\n      }\n    ]);\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    const newPurchase = {\n      id: purchases.length + 1,\n      ...formData,\n      quantity: parseInt(formData.quantity),\n      unitPrice: parseFloat(formData.unitPrice),\n      totalPrice: parseInt(formData.quantity) * parseFloat(formData.unitPrice)\n    };\n\n    setPurchases(prev => [newPurchase, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      itemName: '',\n      quantity: '',\n      unitPrice: '',\n      supplier: '',\n      invoiceNumber: '',\n      notes: ''\n    });\n    setShowForm(false);\n  };\n\n\n\n  const getTotalValue = () => {\n    return purchases.reduce((sum, purchase) => sum + purchase.totalPrice, 0);\n  };\n\n  const getTotalItems = () => {\n    return purchases.reduce((sum, purchase) => sum + purchase.quantity, 0);\n  };\n\n  return (\n    <div className=\"warehouse-purchases\">\n      {/* إحصائيات المخزن */}\n      <div className=\"stats-grid\" style={{ marginBottom: '30px' }}>\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#1e3a8a' }}>\n            {formatCurrency(getTotalValue())}\n          </div>\n          <div className=\"stat-label\">إجمالي قيمة المشتريات</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#ffa500' }}>\n            {getTotalItems()}\n          </div>\n          <div className=\"stat-label\">إجمالي الكمية</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#ea580c' }}>\n            {purchases.length}\n          </div>\n          <div className=\"stat-label\">عدد المشتريات</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#1e40af' }}>\n            {new Set(purchases.map(p => p.supplier)).size}\n          </div>\n          <div className=\"stat-label\">عدد الموردين</div>\n        </div>\n      </div>\n\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">مشتريات المخزن</h3>\n          <button \n            className=\"btn btn-primary\"\n            onClick={() => setShowForm(!showForm)}\n          >\n            ➕ إضافة مشتريات جديدة\n          </button>\n        </div>\n\n        {/* نموذج إضافة مشتريات */}\n        {showForm && (\n          <form onSubmit={handleSubmit} style={{ marginBottom: '20px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">التاريخ</label>\n                <input\n                  type=\"date\"\n                  name=\"date\"\n                  value={formData.date}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  required\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">رقم الفاتورة</label>\n                <input\n                  type=\"text\"\n                  name=\"invoiceNumber\"\n                  value={formData.invoiceNumber}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"رقم الفاتورة\"\n                />\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">اسم الصنف</label>\n              <input\n                type=\"text\"\n                name=\"itemName\"\n                value={formData.itemName}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"اسم الصنف أو المادة\"\n                required\n              />\n            </div>\n\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">الكمية</label>\n                <input\n                  type=\"number\"\n                  name=\"quantity\"\n                  value={formData.quantity}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"الكمية\"\n                  required\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">سعر الوحدة</label>\n                <input\n                  type=\"number\"\n                  name=\"unitPrice\"\n                  value={formData.unitPrice}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"سعر الوحدة\"\n                  step=\"0.01\"\n                  required\n                />\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">المورد</label>\n              <input\n                type=\"text\"\n                name=\"supplier\"\n                value={formData.supplier}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"اسم المورد\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">ملاحظات</label>\n              <textarea\n                name=\"notes\"\n                value={formData.notes}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"ملاحظات إضافية\"\n                rows=\"3\"\n              />\n            </div>\n\n            {formData.quantity && formData.unitPrice && (\n              <div className=\"alert alert-info\">\n                <strong>إجمالي المبلغ: {formatCurrency(formData.quantity * formData.unitPrice)}</strong>\n              </div>\n            )}\n\n            <div style={{ display: 'flex', gap: '10px' }}>\n              <button type=\"submit\" className=\"btn btn-primary\">\n                💾 حفظ المشتريات\n              </button>\n              <button \n                type=\"button\" \n                className=\"btn btn-secondary\"\n                onClick={() => setShowForm(false)}\n              >\n                ❌ إلغاء\n              </button>\n            </div>\n          </form>\n        )}\n\n        {/* جدول المشتريات */}\n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>التاريخ</th>\n                <th>اسم الصنف</th>\n                <th>الكمية</th>\n                <th>سعر الوحدة</th>\n                <th>إجمالي المبلغ</th>\n                <th>المورد</th>\n                <th>رقم الفاتورة</th>\n                <th>إجراءات</th>\n              </tr>\n            </thead>\n            <tbody>\n              {purchases.map((purchase) => (\n                <tr key={purchase.id}>\n                  <td>{new Date(purchase.date).toLocaleDateString('ar-SA')}</td>\n                  <td>{purchase.itemName}</td>\n                  <td>{purchase.quantity} {purchase.unit}</td>\n                  <td>{formatCurrency(purchase.unitPrice)}</td>\n                  <td style={{ fontWeight: 'bold', color: '#1e3a8a' }}>\n                    {formatCurrency(purchase.totalPrice)}\n                  </td>\n                  <td>{purchase.supplier}</td>\n                  <td>{purchase.invoiceNumber}</td>\n                  <td>\n                    <button className=\"btn btn-secondary\" style={{ padding: '5px 10px', fontSize: '12px' }}>\n                      ✏️ تعديل\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default WarehousePurchases;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,EAAEC,UAAU,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC;IACvCc,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5CC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEFtB,SAAS,CAAC,MAAM;IACd;IACAQ,YAAY,CAAC,CACX;MACEe,EAAE,EAAE,CAAC;MACLV,IAAI,EAAE,YAAY;MAClBI,QAAQ,EAAE,6BAA6B;MACvCC,QAAQ,EAAE,GAAG;MACbC,SAAS,EAAE,EAAE;MACbK,UAAU,EAAE,IAAI;MAChBJ,QAAQ,EAAE,qBAAqB;MAC/BC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE,0BAA0B;MACjCG,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLV,IAAI,EAAE,YAAY;MAClBI,QAAQ,EAAE,oBAAoB;MAC9BC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,GAAG;MACdK,UAAU,EAAE,IAAI;MAChBJ,QAAQ,EAAE,wBAAwB;MAClCC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE,mBAAmB;MAC1BG,QAAQ,EAAE,OAAO;MACjBC,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLV,IAAI,EAAE,YAAY;MAClBI,QAAQ,EAAE,sBAAsB;MAChCC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbK,UAAU,EAAE,IAAI;MAChBJ,QAAQ,EAAE,0BAA0B;MACpCC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE,mBAAmB;MAC1BG,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLV,IAAI,EAAE,YAAY;MAClBI,QAAQ,EAAE,eAAe;MACzBC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,GAAG;MACdK,UAAU,EAAE,IAAI;MAChBJ,QAAQ,EAAE,iBAAiB;MAC3BC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE,kBAAkB;MACzBG,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLV,IAAI,EAAE,YAAY;MAClBI,QAAQ,EAAE,mBAAmB;MAC7BC,QAAQ,EAAE,GAAG;MACbC,SAAS,EAAE,EAAE;MACbK,UAAU,EAAE,IAAI;MAChBJ,QAAQ,EAAE,sBAAsB;MAChCC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE,oBAAoB;MAC3BG,QAAQ,EAAE,OAAO;MACjBC,IAAI,EAAE;IACR,CAAC,CACF,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCnB,WAAW,CAACoB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAIL,CAAC,IAAK;IAC1BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,MAAMC,WAAW,GAAG;MAClBZ,EAAE,EAAEhB,SAAS,CAAC6B,MAAM,GAAG,CAAC;MACxB,GAAGzB,QAAQ;MACXO,QAAQ,EAAEmB,QAAQ,CAAC1B,QAAQ,CAACO,QAAQ,CAAC;MACrCC,SAAS,EAAEmB,UAAU,CAAC3B,QAAQ,CAACQ,SAAS,CAAC;MACzCK,UAAU,EAAEa,QAAQ,CAAC1B,QAAQ,CAACO,QAAQ,CAAC,GAAGoB,UAAU,CAAC3B,QAAQ,CAACQ,SAAS;IACzE,CAAC;IAEDX,YAAY,CAACwB,IAAI,IAAI,CAACG,WAAW,EAAE,GAAGH,IAAI,CAAC,CAAC;IAC5CpB,WAAW,CAAC;MACVC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBC,KAAK,EAAE;IACT,CAAC,CAAC;IACFZ,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAID,MAAM6B,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOhC,SAAS,CAACiC,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKD,GAAG,GAAGC,QAAQ,CAAClB,UAAU,EAAE,CAAC,CAAC;EAC1E,CAAC;EAED,MAAMmB,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOpC,SAAS,CAACiC,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKD,GAAG,GAAGC,QAAQ,CAACxB,QAAQ,EAAE,CAAC,CAAC;EACxE,CAAC;EAED,oBACEd,OAAA;IAAKwC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElCzC,OAAA;MAAKwC,SAAS,EAAC,YAAY;MAACE,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBAC1DzC,OAAA;QAAKwC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzC,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrD5C,cAAc,CAACsC,aAAa,CAAC,CAAC;QAAC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACNhD,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAqB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAENhD,OAAA;QAAKwC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzC,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrDF,aAAa,CAAC;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACNhD,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eAENhD,OAAA;QAAKwC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzC,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrDtC,SAAS,CAAC6B;QAAM;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNhD,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eAENhD,OAAA;QAAKwC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzC,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrD,IAAIQ,GAAG,CAAC9C,SAAS,CAAC+C,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnC,QAAQ,CAAC,CAAC,CAACoC;QAAI;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNhD,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAY;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhD,OAAA;MAAKwC,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBzC,OAAA;QAAKwC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzC,OAAA;UAAIwC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9ChD,OAAA;UACEwC,SAAS,EAAC,iBAAiB;UAC3Ba,OAAO,EAAEA,CAAA,KAAM/C,WAAW,CAAC,CAACD,QAAQ,CAAE;UAAAoC,QAAA,EACvC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL3C,QAAQ,iBACPL,OAAA;QAAMsD,QAAQ,EAAEzB,YAAa;QAACa,KAAK,EAAE;UAAEC,YAAY,EAAE,MAAM;UAAEY,OAAO,EAAE,MAAM;UAAEC,eAAe,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAhB,QAAA,gBAC9HzC,OAAA;UAAKwC,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBzC,OAAA;YAAKwC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBzC,OAAA;cAAOwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7ChD,OAAA;cACE0D,IAAI,EAAC,MAAM;cACXjC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEnB,QAAQ,CAACE,IAAK;cACrBkD,QAAQ,EAAEpC,iBAAkB;cAC5BiB,SAAS,EAAC,YAAY;cACtBoB,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhD,OAAA;YAAKwC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBzC,OAAA;cAAOwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClDhD,OAAA;cACE0D,IAAI,EAAC,MAAM;cACXjC,IAAI,EAAC,eAAe;cACpBC,KAAK,EAAEnB,QAAQ,CAACU,aAAc;cAC9B0C,QAAQ,EAAEpC,iBAAkB;cAC5BiB,SAAS,EAAC,YAAY;cACtBqB,WAAW,EAAC;YAAc;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhD,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzC,OAAA;YAAOwC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/ChD,OAAA;YACE0D,IAAI,EAAC,MAAM;YACXjC,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEnB,QAAQ,CAACM,QAAS;YACzB8C,QAAQ,EAAEpC,iBAAkB;YAC5BiB,SAAS,EAAC,YAAY;YACtBqB,WAAW,EAAC,qGAAqB;YACjCD,QAAQ;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENhD,OAAA;UAAKwC,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBzC,OAAA;YAAKwC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBzC,OAAA;cAAOwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5ChD,OAAA;cACE0D,IAAI,EAAC,QAAQ;cACbjC,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEnB,QAAQ,CAACO,QAAS;cACzB6C,QAAQ,EAAEpC,iBAAkB;cAC5BiB,SAAS,EAAC,YAAY;cACtBqB,WAAW,EAAC,sCAAQ;cACpBD,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhD,OAAA;YAAKwC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBzC,OAAA;cAAOwC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDhD,OAAA;cACE0D,IAAI,EAAC,QAAQ;cACbjC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAEnB,QAAQ,CAACQ,SAAU;cAC1B4C,QAAQ,EAAEpC,iBAAkB;cAC5BiB,SAAS,EAAC,YAAY;cACtBqB,WAAW,EAAC,yDAAY;cACxBC,IAAI,EAAC,MAAM;cACXF,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhD,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzC,OAAA;YAAOwC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5ChD,OAAA;YACE0D,IAAI,EAAC,MAAM;YACXjC,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEnB,QAAQ,CAACS,QAAS;YACzB2C,QAAQ,EAAEpC,iBAAkB;YAC5BiB,SAAS,EAAC,YAAY;YACtBqB,WAAW,EAAC;UAAY;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENhD,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzC,OAAA;YAAOwC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7ChD,OAAA;YACEyB,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEnB,QAAQ,CAACW,KAAM;YACtByC,QAAQ,EAAEpC,iBAAkB;YAC5BiB,SAAS,EAAC,YAAY;YACtBqB,WAAW,EAAC,iFAAgB;YAC5BE,IAAI,EAAC;UAAG;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELzC,QAAQ,CAACO,QAAQ,IAAIP,QAAQ,CAACQ,SAAS,iBACtCf,OAAA;UAAKwC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BzC,OAAA;YAAAyC,QAAA,GAAQ,6EAAe,EAAC5C,cAAc,CAACU,QAAQ,CAACO,QAAQ,GAAGP,QAAQ,CAACQ,SAAS,CAAC;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CACN,eAEDhD,OAAA;UAAK0C,KAAK,EAAE;YAAEsB,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAxB,QAAA,gBAC3CzC,OAAA;YAAQ0D,IAAI,EAAC,QAAQ;YAAClB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAElD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThD,OAAA;YACE0D,IAAI,EAAC,QAAQ;YACblB,SAAS,EAAC,mBAAmB;YAC7Ba,OAAO,EAAEA,CAAA,KAAM/C,WAAW,CAAC,KAAK,CAAE;YAAAmC,QAAA,EACnC;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP,eAGDhD,OAAA;QAAKwC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BzC,OAAA;UAAOwC,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtBzC,OAAA;YAAAyC,QAAA,eACEzC,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAAyC,QAAA,EAAI;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBhD,OAAA;gBAAAyC,QAAA,EAAI;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBhD,OAAA;gBAAAyC,QAAA,EAAI;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfhD,OAAA;gBAAAyC,QAAA,EAAI;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBhD,OAAA;gBAAAyC,QAAA,EAAI;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBhD,OAAA;gBAAAyC,QAAA,EAAI;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfhD,OAAA;gBAAAyC,QAAA,EAAI;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBhD,OAAA;gBAAAyC,QAAA,EAAI;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRhD,OAAA;YAAAyC,QAAA,EACGtC,SAAS,CAAC+C,GAAG,CAAEZ,QAAQ,iBACtBtC,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAAyC,QAAA,EAAK,IAAI/B,IAAI,CAAC4B,QAAQ,CAAC7B,IAAI,CAAC,CAACyD,kBAAkB,CAAC,OAAO;cAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9DhD,OAAA;gBAAAyC,QAAA,EAAKH,QAAQ,CAACzB;cAAQ;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5BhD,OAAA;gBAAAyC,QAAA,GAAKH,QAAQ,CAACxB,QAAQ,EAAC,GAAC,EAACwB,QAAQ,CAAChB,IAAI;cAAA;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5ChD,OAAA;gBAAAyC,QAAA,EAAK5C,cAAc,CAACyC,QAAQ,CAACvB,SAAS;cAAC;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7ChD,OAAA;gBAAI0C,KAAK,EAAE;kBAAEyB,UAAU,EAAE,MAAM;kBAAEvB,KAAK,EAAE;gBAAU,CAAE;gBAAAH,QAAA,EACjD5C,cAAc,CAACyC,QAAQ,CAAClB,UAAU;cAAC;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACLhD,OAAA;gBAAAyC,QAAA,EAAKH,QAAQ,CAACtB;cAAQ;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5BhD,OAAA;gBAAAyC,QAAA,EAAKH,QAAQ,CAACrB;cAAa;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjChD,OAAA;gBAAAyC,QAAA,eACEzC,OAAA;kBAAQwC,SAAS,EAAC,mBAAmB;kBAACE,KAAK,EAAE;oBAAEa,OAAO,EAAE,UAAU;oBAAEa,QAAQ,EAAE;kBAAO,CAAE;kBAAA3B,QAAA,EAAC;gBAExF;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAdEV,QAAQ,CAACnB,EAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAehB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9C,EAAA,CAtUID,kBAAkB;AAAAoE,EAAA,GAAlBpE,kBAAkB;AAwUxB,eAAeA,kBAAkB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}