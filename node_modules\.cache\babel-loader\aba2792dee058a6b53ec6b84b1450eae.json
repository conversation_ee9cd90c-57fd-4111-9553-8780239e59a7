{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\Sidebar.js\";\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nconst Sidebar = () => {\n  const location = useLocation();\n  const menuItems = [{\n    path: '/',\n    label: 'لوحة التحكم',\n    icon: '📊'\n  }, {\n    path: '/account-statements',\n    label: 'كشف الحساب',\n    icon: '📋'\n  }, {\n    path: '/warehouse-purchases',\n    label: 'مشتريات المخزن',\n    icon: '📦'\n  }, {\n    path: '/transport-expenses',\n    label: 'مصروفات النقل',\n    icon: '🚛'\n  }, {\n    path: '/living-expenses',\n    label: 'مصروفات المعيشة',\n    icon: '🏠'\n  }, {\n    path: '/paint-expenses',\n    label: 'مصروفات الطلاء',\n    icon: '🎨'\n  }, {\n    path: '/factory-expenses',\n    label: 'مصروفات المصنع',\n    icon: '🏭'\n  }, {\n    path: '/employee-withdrawals',\n    label: 'مسحوبات الموظفين',\n    icon: '👥'\n  }, {\n    path: '/treasury-deposits',\n    label: 'إيداعات الخزينة',\n    icon: '💰'\n  }, {\n    path: '/reports',\n    label: 'التقارير',\n    icon: '📈'\n  }];\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"sidebar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"sidebar-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 9\n    }\n  }, \"\\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 9\n    }\n  }, \"\\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631 1.0\")), /*#__PURE__*/React.createElement(\"nav\", {\n    className: \"sidebar-menu\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }\n  }, menuItems.map(item => /*#__PURE__*/React.createElement(Link, {\n    key: item.path,\n    to: item.path,\n    className: `menu-item ${location.pathname === item.path ? 'active' : ''}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"menu-icon\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 13\n    }\n  }, item.icon), item.label))));\n};\nexport default Sidebar;", "map": {"version": 3, "names": ["React", "Link", "useLocation", "Sidebar", "location", "menuItems", "path", "label", "icon", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "key", "to", "pathname"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/Sidebar.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\nconst Sidebar = () => {\n  const location = useLocation();\n\n  const menuItems = [\n    {\n      path: '/',\n      label: 'لوحة التحكم',\n      icon: '📊'\n    },\n    {\n      path: '/account-statements',\n      label: 'كشف الحساب',\n      icon: '📋'\n    },\n    {\n      path: '/warehouse-purchases',\n      label: 'مشتريات المخزن',\n      icon: '📦'\n    },\n    {\n      path: '/transport-expenses',\n      label: 'مصروفات النقل',\n      icon: '🚛'\n    },\n    {\n      path: '/living-expenses',\n      label: 'مصروفات المعيشة',\n      icon: '🏠'\n    },\n    {\n      path: '/paint-expenses',\n      label: 'مصروفات الطلاء',\n      icon: '🎨'\n    },\n    {\n      path: '/factory-expenses',\n      label: 'مصروفات المصنع',\n      icon: '🏭'\n    },\n    {\n      path: '/employee-withdrawals',\n      label: 'مسحوبات الموظفين',\n      icon: '👥'\n    },\n    {\n      path: '/treasury-deposits',\n      label: 'إيداعات الخزينة',\n      icon: '💰'\n    },\n    {\n      path: '/reports',\n      label: 'التقارير',\n      icon: '📈'\n    }\n  ];\n\n  return (\n    <div className=\"sidebar\">\n      <div className=\"sidebar-header\">\n        <h2>نظام المحاسبة</h2>\n        <p>الإصدار 1.0</p>\n      </div>\n      \n      <nav className=\"sidebar-menu\">\n        {menuItems.map((item) => (\n          <Link\n            key={item.path}\n            to={item.path}\n            className={`menu-item ${location.pathname === item.path ? 'active' : ''}`}\n          >\n            <span className=\"menu-icon\">{item.icon}</span>\n            {item.label}\n          </Link>\n        ))}\n      </nav>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAEpD,MAAMC,OAAO,GAAGA,CAAA,KAAM;EACpB,MAAMC,QAAQ,GAAGF,WAAW,CAAC,CAAC;EAE9B,MAAMG,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACER,KAAA,CAAAS,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBhB,KAAA,CAAAS,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BhB,KAAA,CAAAS,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,2EAAiB,CAAC,eACtBhB,KAAA,CAAAS,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,gDAAc,CACd,CAAC,eAENhB,KAAA,CAAAS,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1BX,SAAS,CAACY,GAAG,CAAEC,IAAI,iBAClBlB,KAAA,CAAAS,aAAA,CAACR,IAAI;IACHkB,GAAG,EAAED,IAAI,CAACZ,IAAK;IACfc,EAAE,EAAEF,IAAI,CAACZ,IAAK;IACdI,SAAS,EAAE,aAAaN,QAAQ,CAACiB,QAAQ,KAAKH,IAAI,CAACZ,IAAI,GAAG,QAAQ,GAAG,EAAE,EAAG;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE1EhB,KAAA,CAAAS,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEE,IAAI,CAACV,IAAW,CAAC,EAC7CU,IAAI,CAACX,KACF,CACP,CACE,CACF,CAAC;AAEV,CAAC;AAED,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}