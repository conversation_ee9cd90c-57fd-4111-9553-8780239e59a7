{"ast": null, "code": "/**\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\n// TODO: we might want to make this injectable to support DEV-time non-root URLs.\nmodule.exports = '/__open-stack-frame-in-editor';", "map": {"version": 3, "names": ["module", "exports"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/node_modules/react-dev-utils/launchEditorEndpoint.js"], "sourcesContent": ["/**\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\n// TODO: we might want to make this injectable to support DEV-time non-root URLs.\nmodule.exports = '/__open-stack-frame-in-editor';\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAG,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "script"}