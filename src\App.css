* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Cairo', sans-serif;
}

body {
  direction: rtl;
  text-align: right;
  background-color: #f5f7fa;
  color: #333;
}

.app {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f7fa;
}

/* الشريط الجانبي */
.sidebar {
  width: 280px;
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  color: white;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h2 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.sidebar-header p {
  font-size: 12px;
  opacity: 0.8;
}

.sidebar-menu {
  flex: 1;
  padding: 20px 0;
}

.menu-item {
  display: block;
  padding: 12px 20px;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  border-right: 3px solid transparent;
}

.menu-item:hover {
  background-color: rgba(255,165,0,0.2);
  border-right-color: #ffa500;
}

.menu-item.active {
  background-color: rgba(255,165,0,0.3);
  border-right-color: #ffa500;
}

.menu-item i {
  margin-left: 10px;
  width: 20px;
}

/* الهيدر */
.header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 20px 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 3px solid #1e3a8a;
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  font-size: 28px;
  color: #1e3a8a;
  font-weight: 700;
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.date-time-info {
  display: flex;
  gap: 20px;
  align-items: center;
}

.current-date, .current-time {
  font-size: 14px;
  color: #666;
  background: rgba(30, 58, 138, 0.1);
  padding: 5px 12px;
  border-radius: 20px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.user-info {
  margin-left: 15px;
  padding-left: 15px;
  border-left: 2px solid #e9ecef;
}

.user-name {
  font-size: 14px;
  color: #1e3a8a;
  font-weight: 600;
}

.btn {
  padding: 10px 18px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #1e3a8a 0%, #ffa500 100%);
  color: white;
  border: 2px solid transparent;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);
  background: linear-gradient(135deg, #1e40af 0%, #fb923c 100%);
}

.btn-secondary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  border: 2px solid #dee2e6;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: 2px solid transparent;
}

.btn-success:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
  background: linear-gradient(135deg, #218838 0%, #1dd1a1 100%);
}

.btn-danger {
  background: linear-gradient(135deg, #dc3545 0%, #fd5e53 100%);
  color: white;
  border: 2px solid transparent;
}

.btn-danger:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
  background: linear-gradient(135deg, #c82333 0%, #e04347 100%);
}

/* البطاقات */
.card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  margin-bottom: 25px;
  border: 1px solid rgba(30, 58, 138, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1e3a8a 0%, #ffa500 100%);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(30, 58, 138, 0.1);
}

.card-title {
  font-size: 20px;
  font-weight: 700;
  color: #1e3a8a;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* النماذج */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #1e3a8a;
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

/* الجداول */
.table-container {
  overflow-x: auto;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border: 1px solid rgba(30, 58, 138, 0.1);
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  font-size: 14px;
}

.table th,
.table td {
  padding: 15px 12px;
  text-align: right;
  border-bottom: 1px solid rgba(30, 58, 138, 0.1);
  transition: all 0.3s ease;
}

.table th {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  font-weight: 700;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 13px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table tbody tr {
  transition: all 0.3s ease;
}

.table tbody tr:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transform: scale(1.01);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table tbody tr:nth-child(even) {
  background: rgba(30, 58, 138, 0.02);
}

/* الإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin-bottom: 35px;
}

.stat-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 25px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  text-align: center;
  border: 1px solid rgba(30, 58, 138, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1e3a8a 0%, #ffa500 100%);
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 45px rgba(0,0,0,0.15);
}

.stat-value {
  font-size: 32px;
  font-weight: 800;
  margin-bottom: 8px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  background: linear-gradient(135deg, #1e3a8a 0%, #ffa500 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 16px;
  color: #495057;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* الرسائل */
.alert {
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.alert-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* التحميل */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  font-size: 16px;
  color: #666;
}

/* الشارات */
.badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.badge:hover {
  transform: scale(1.05);
}

.badge-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.badge-danger {
  background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
  color: white;
}

.badge-warning {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  color: #212529;
}

.badge-info {
  background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
  color: white;
}

/* تحسينات إضافية */
.menu-icon {
  margin-left: 10px;
  font-size: 16px;
}

.sidebar-menu .menu-item {
  font-size: 14px;
  font-weight: 500;
}

/* الإجراءات السريعة */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  text-align: right;
  min-height: 80px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.quick-action-btn:hover {
  transform: translateY(-5px) scale(1.02);
}

.action-icon {
  font-size: 32px;
  min-width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-text {
  display: flex;
  flex-direction: column;
  gap: 5px;
  flex: 1;
}

.action-title {
  font-size: 16px;
  font-weight: 700;
  line-height: 1.2;
}

.action-desc {
  font-size: 13px;
  opacity: 0.8;
  line-height: 1.3;
}

/* تحسينات إضافية للتطبيق */
.dashboard {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* الاستجابة */
@media (max-width: 768px) {
  .sidebar {
    width: 250px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
  }

  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
  }

  .date-time-info {
    flex-direction: column;
    gap: 10px;
  }
}
