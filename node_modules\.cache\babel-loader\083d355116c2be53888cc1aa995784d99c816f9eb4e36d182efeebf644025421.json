{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\AccountStatements.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { formatCurrency, formatDate } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AccountStatements = () => {\n  _s();\n  const [statements, setStatements] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    description: '',\n    debit: '',\n    credit: '',\n    accountType: 'عام'\n  });\n  useEffect(() => {\n    // جلب البيانات من قاعدة البيانات\n    // مؤقتاً سنستخدم بيانات تجريبية\n    setStatements([{\n      id: 1,\n      date: '2024-01-15',\n      description: 'إيداع نقدي',\n      debit: 0,\n      credit: 25000,\n      balance: 25000,\n      accountType: 'خزينة'\n    }, {\n      id: 2,\n      date: '2024-01-14',\n      description: 'مشتريات مواد خام',\n      debit: 8500,\n      credit: 0,\n      balance: 16500,\n      accountType: 'مخزن'\n    }, {\n      id: 3,\n      date: '2024-01-13',\n      description: 'مصروفات نقل',\n      debit: 3200,\n      credit: 0,\n      balance: 13300,\n      accountType: 'نقل'\n    }]);\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    const newStatement = {\n      id: statements.length + 1,\n      ...formData,\n      debit: parseFloat(formData.debit) || 0,\n      credit: parseFloat(formData.credit) || 0,\n      balance: calculateNewBalance()\n    };\n    setStatements(prev => [newStatement, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      description: '',\n      debit: '',\n      credit: '',\n      accountType: 'عام'\n    });\n    setShowForm(false);\n  };\n  const calculateNewBalance = () => {\n    const lastBalance = statements.length > 0 ? statements[0].balance : 0;\n    const debit = parseFloat(formData.debit) || 0;\n    const credit = parseFloat(formData.credit) || 0;\n    return lastBalance + credit - debit;\n  };\n  const getTotalDebit = () => {\n    return statements.reduce((sum, statement) => sum + statement.debit, 0);\n  };\n  const getTotalCredit = () => {\n    return statements.reduce((sum, statement) => sum + statement.credit, 0);\n  };\n  const getCurrentBalance = () => {\n    return getTotalCredit() - getTotalDebit();\n  };\n  const handleDelete = id => {\n    if (window.confirm('هل أنت متأكد من حذف هذا القيد؟')) {\n      setStatements(prev => prev.filter(statement => statement.id !== id));\n    }\n  };\n  const handleEdit = id => {\n    const statement = statements.find(s => s.id === id);\n    if (statement) {\n      setFormData({\n        date: statement.date,\n        description: statement.description,\n        accountType: statement.accountType,\n        debit: statement.debit.toString(),\n        credit: statement.credit.toString()\n      });\n      setEditingId(id);\n      setShowForm(true);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"account-statements\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#dc3545'\n          },\n          children: formatCurrency(getTotalDebit())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u062F\\u064A\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#ffa500'\n          },\n          children: formatCurrency(getTotalCredit())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u062F\\u0627\\u0626\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#1e3a8a'\n          },\n          children: formatCurrency(getCurrentBalance())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0643\\u0634\\u0641 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => setShowForm(!showForm),\n          children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0642\\u064A\\u062F \\u062C\\u062F\\u064A\\u062F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), showForm && /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        style: {\n          marginBottom: '20px',\n          padding: '20px',\n          backgroundColor: '#f8f9fa',\n          borderRadius: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              name: \"date\",\n              value: formData.date,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"accountType\",\n              value: formData.accountType,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0639\\u0627\\u0645\",\n                children: \"\\u0639\\u0627\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u062E\\u0632\\u064A\\u0646\\u0629\",\n                children: \"\\u062E\\u0632\\u064A\\u0646\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0645\\u062E\\u0632\\u0646\",\n                children: \"\\u0645\\u062E\\u0632\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0646\\u0642\\u0644\",\n                children: \"\\u0646\\u0642\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0645\\u0639\\u064A\\u0634\\u0629\",\n                children: \"\\u0645\\u0639\\u064A\\u0634\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0637\\u0644\\u0627\\u0621\",\n                children: \"\\u0637\\u0644\\u0627\\u0621\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0645\\u0635\\u0646\\u0639\",\n                children: \"\\u0645\\u0635\\u0646\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"description\",\n            value: formData.description,\n            onChange: handleInputChange,\n            className: \"form-input\",\n            placeholder: \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0645\\u0639\\u0627\\u0645\\u0644\\u0629\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0645\\u062F\\u064A\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"debit\",\n              value: formData.debit,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"0.00\",\n              step: \"0.01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u062F\\u0627\\u0626\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"credit\",\n              value: formData.credit,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"0.00\",\n              step: \"0.01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: \"\\uD83D\\uDCBE \\u062D\\u0641\\u0638 \\u0627\\u0644\\u0642\\u064A\\u062F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn btn-secondary\",\n            onClick: () => setShowForm(false),\n            children: \"\\u274C \\u0625\\u0644\\u063A\\u0627\\u0621\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0645\\u062F\\u064A\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u062F\\u0627\\u0626\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: statements.map(statement => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatDate(statement.date)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: statement.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge badge-info\",\n                  children: statement.accountType\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  color: statement.debit > 0 ? '#dc3545' : '#666'\n                },\n                children: statement.debit > 0 ? formatCurrency(statement.debit) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  color: statement.credit > 0 ? '#ffa500' : '#666'\n                },\n                children: statement.credit > 0 ? formatCurrency(statement.credit) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  fontWeight: 'bold',\n                  color: statement.balance >= 0 ? '#1e3a8a' : '#dc3545'\n                },\n                children: formatCurrency(statement.balance)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '5px 10px',\n                    fontSize: '12px'\n                  },\n                  children: \"\\u270F\\uFE0F \\u062A\\u0639\\u062F\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this)]\n            }, statement.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountStatements, \"7bAKIV1eHvpDzANl8d6G50mA5Qw=\");\n_c = AccountStatements;\nexport default AccountStatements;\nvar _c;\n$RefreshReg$(_c, \"AccountStatements\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "formatCurrency", "formatDate", "jsxDEV", "_jsxDEV", "AccountStatements", "_s", "statements", "setStatements", "showForm", "setShowForm", "formData", "setFormData", "date", "Date", "toISOString", "split", "description", "debit", "credit", "accountType", "id", "balance", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "newStatement", "length", "parseFloat", "calculateNewBalance", "lastBalance", "getTotalDebit", "reduce", "sum", "statement", "getTotalCredit", "getCurrentBalance", "handleDelete", "window", "confirm", "filter", "handleEdit", "find", "s", "toString", "setEditingId", "className", "children", "style", "marginBottom", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "padding", "backgroundColor", "borderRadius", "type", "onChange", "required", "placeholder", "step", "display", "gap", "map", "fontWeight", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/AccountStatements.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { formatCurrency, formatDate } from '../utils/currency';\n\nconst AccountStatements = () => {\n  const [statements, setStatements] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    description: '',\n    debit: '',\n    credit: '',\n    accountType: 'عام'\n  });\n\n  useEffect(() => {\n    // جلب البيانات من قاعدة البيانات\n    // مؤقتاً سنستخدم بيانات تجريبية\n    setStatements([\n      {\n        id: 1,\n        date: '2024-01-15',\n        description: 'إيداع نقدي',\n        debit: 0,\n        credit: 25000,\n        balance: 25000,\n        accountType: 'خزينة'\n      },\n      {\n        id: 2,\n        date: '2024-01-14',\n        description: 'مشتريات مواد خام',\n        debit: 8500,\n        credit: 0,\n        balance: 16500,\n        accountType: 'مخزن'\n      },\n      {\n        id: 3,\n        date: '2024-01-13',\n        description: 'مصروفات نقل',\n        debit: 3200,\n        credit: 0,\n        balance: 13300,\n        accountType: 'نقل'\n      }\n    ]);\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    const newStatement = {\n      id: statements.length + 1,\n      ...formData,\n      debit: parseFloat(formData.debit) || 0,\n      credit: parseFloat(formData.credit) || 0,\n      balance: calculateNewBalance()\n    };\n\n    setStatements(prev => [newStatement, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      description: '',\n      debit: '',\n      credit: '',\n      accountType: 'عام'\n    });\n    setShowForm(false);\n  };\n\n  const calculateNewBalance = () => {\n    const lastBalance = statements.length > 0 ? statements[0].balance : 0;\n    const debit = parseFloat(formData.debit) || 0;\n    const credit = parseFloat(formData.credit) || 0;\n    return lastBalance + credit - debit;\n  };\n\n\n\n  const getTotalDebit = () => {\n    return statements.reduce((sum, statement) => sum + statement.debit, 0);\n  };\n\n  const getTotalCredit = () => {\n    return statements.reduce((sum, statement) => sum + statement.credit, 0);\n  };\n\n  const getCurrentBalance = () => {\n    return getTotalCredit() - getTotalDebit();\n  };\n\n  const handleDelete = (id) => {\n    if (window.confirm('هل أنت متأكد من حذف هذا القيد؟')) {\n      setStatements(prev => prev.filter(statement => statement.id !== id));\n    }\n  };\n\n  const handleEdit = (id) => {\n    const statement = statements.find(s => s.id === id);\n    if (statement) {\n      setFormData({\n        date: statement.date,\n        description: statement.description,\n        accountType: statement.accountType,\n        debit: statement.debit.toString(),\n        credit: statement.credit.toString()\n      });\n      setEditingId(id);\n      setShowForm(true);\n    }\n  };\n\n  return (\n    <div className=\"account-statements\">\n      {/* ملخص الحساب */}\n      <div className=\"stats-grid\" style={{ marginBottom: '30px' }}>\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#dc3545' }}>\n            {formatCurrency(getTotalDebit())}\n          </div>\n          <div className=\"stat-label\">إجمالي المدين</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#ffa500' }}>\n            {formatCurrency(getTotalCredit())}\n          </div>\n          <div className=\"stat-label\">إجمالي الدائن</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#1e3a8a' }}>\n            {formatCurrency(getCurrentBalance())}\n          </div>\n          <div className=\"stat-label\">الرصيد الحالي</div>\n        </div>\n      </div>\n\n      {/* كشف الحساب */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">كشف الحساب</h3>\n          <button \n            className=\"btn btn-primary\"\n            onClick={() => setShowForm(!showForm)}\n          >\n            ➕ إضافة قيد جديد\n          </button>\n        </div>\n\n        {/* نموذج إضافة قيد */}\n        {showForm && (\n          <form onSubmit={handleSubmit} style={{ marginBottom: '20px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">التاريخ</label>\n                <input\n                  type=\"date\"\n                  name=\"date\"\n                  value={formData.date}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  required\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">نوع الحساب</label>\n                <select\n                  name=\"accountType\"\n                  value={formData.accountType}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  required\n                >\n                  <option value=\"عام\">عام</option>\n                  <option value=\"خزينة\">خزينة</option>\n                  <option value=\"مخزن\">مخزن</option>\n                  <option value=\"نقل\">نقل</option>\n                  <option value=\"معيشة\">معيشة</option>\n                  <option value=\"طلاء\">طلاء</option>\n                  <option value=\"مصنع\">مصنع</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">الوصف</label>\n              <input\n                type=\"text\"\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"وصف المعاملة\"\n                required\n              />\n            </div>\n\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">مدين</label>\n                <input\n                  type=\"number\"\n                  name=\"debit\"\n                  value={formData.debit}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"0.00\"\n                  step=\"0.01\"\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">دائن</label>\n                <input\n                  type=\"number\"\n                  name=\"credit\"\n                  value={formData.credit}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"0.00\"\n                  step=\"0.01\"\n                />\n              </div>\n            </div>\n\n            <div style={{ display: 'flex', gap: '10px' }}>\n              <button type=\"submit\" className=\"btn btn-primary\">\n                💾 حفظ القيد\n              </button>\n              <button \n                type=\"button\" \n                className=\"btn btn-secondary\"\n                onClick={() => setShowForm(false)}\n              >\n                ❌ إلغاء\n              </button>\n            </div>\n          </form>\n        )}\n\n        {/* جدول كشف الحساب */}\n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>التاريخ</th>\n                <th>الوصف</th>\n                <th>نوع الحساب</th>\n                <th>مدين</th>\n                <th>دائن</th>\n                <th>الرصيد</th>\n                <th>إجراءات</th>\n              </tr>\n            </thead>\n            <tbody>\n              {statements.map((statement) => (\n                <tr key={statement.id}>\n                  <td>{formatDate(statement.date)}</td>\n                  <td>{statement.description}</td>\n                  <td>\n                    <span className=\"badge badge-info\">{statement.accountType}</span>\n                  </td>\n                  <td style={{ color: statement.debit > 0 ? '#dc3545' : '#666' }}>\n                    {statement.debit > 0 ? formatCurrency(statement.debit) : '-'}\n                  </td>\n                  <td style={{ color: statement.credit > 0 ? '#ffa500' : '#666' }}>\n                    {statement.credit > 0 ? formatCurrency(statement.credit) : '-'}\n                  </td>\n                  <td style={{\n                    fontWeight: 'bold',\n                    color: statement.balance >= 0 ? '#1e3a8a' : '#dc3545'\n                  }}>\n                    {formatCurrency(statement.balance)}\n                  </td>\n                  <td>\n                    <button className=\"btn btn-secondary\" style={{ padding: '5px 10px', fontSize: '12px' }}>\n                      ✏️ تعديل\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AccountStatements;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,EAAEC,UAAU,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC;IACvCc,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5CC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;EACf,CAAC,CAAC;EAEFpB,SAAS,CAAC,MAAM;IACd;IACA;IACAQ,aAAa,CAAC,CACZ;MACEa,EAAE,EAAE,CAAC;MACLR,IAAI,EAAE,YAAY;MAClBI,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,KAAK;MACbG,OAAO,EAAE,KAAK;MACdF,WAAW,EAAE;IACf,CAAC,EACD;MACEC,EAAE,EAAE,CAAC;MACLR,IAAI,EAAE,YAAY;MAClBI,WAAW,EAAE,kBAAkB;MAC/BC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,CAAC;MACTG,OAAO,EAAE,KAAK;MACdF,WAAW,EAAE;IACf,CAAC,EACD;MACEC,EAAE,EAAE,CAAC;MACLR,IAAI,EAAE,YAAY;MAClBI,WAAW,EAAE,aAAa;MAC1BC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,CAAC;MACTG,OAAO,EAAE,KAAK;MACdF,WAAW,EAAE;IACf,CAAC,CACF,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCf,WAAW,CAACgB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAIL,CAAC,IAAK;IAC1BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,MAAMC,YAAY,GAAG;MACnBV,EAAE,EAAEd,UAAU,CAACyB,MAAM,GAAG,CAAC;MACzB,GAAGrB,QAAQ;MACXO,KAAK,EAAEe,UAAU,CAACtB,QAAQ,CAACO,KAAK,CAAC,IAAI,CAAC;MACtCC,MAAM,EAAEc,UAAU,CAACtB,QAAQ,CAACQ,MAAM,CAAC,IAAI,CAAC;MACxCG,OAAO,EAAEY,mBAAmB,CAAC;IAC/B,CAAC;IAED1B,aAAa,CAACoB,IAAI,IAAI,CAACG,YAAY,EAAE,GAAGH,IAAI,CAAC,CAAC;IAC9ChB,WAAW,CAAC;MACVC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE;IACf,CAAC,CAAC;IACFV,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMwB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,WAAW,GAAG5B,UAAU,CAACyB,MAAM,GAAG,CAAC,GAAGzB,UAAU,CAAC,CAAC,CAAC,CAACe,OAAO,GAAG,CAAC;IACrE,MAAMJ,KAAK,GAAGe,UAAU,CAACtB,QAAQ,CAACO,KAAK,CAAC,IAAI,CAAC;IAC7C,MAAMC,MAAM,GAAGc,UAAU,CAACtB,QAAQ,CAACQ,MAAM,CAAC,IAAI,CAAC;IAC/C,OAAOgB,WAAW,GAAGhB,MAAM,GAAGD,KAAK;EACrC,CAAC;EAID,MAAMkB,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAO7B,UAAU,CAAC8B,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAKD,GAAG,GAAGC,SAAS,CAACrB,KAAK,EAAE,CAAC,CAAC;EACxE,CAAC;EAED,MAAMsB,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOjC,UAAU,CAAC8B,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAKD,GAAG,GAAGC,SAAS,CAACpB,MAAM,EAAE,CAAC,CAAC;EACzE,CAAC;EAED,MAAMsB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAOD,cAAc,CAAC,CAAC,GAAGJ,aAAa,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMM,YAAY,GAAIrB,EAAE,IAAK;IAC3B,IAAIsB,MAAM,CAACC,OAAO,CAAC,gCAAgC,CAAC,EAAE;MACpDpC,aAAa,CAACoB,IAAI,IAAIA,IAAI,CAACiB,MAAM,CAACN,SAAS,IAAIA,SAAS,CAAClB,EAAE,KAAKA,EAAE,CAAC,CAAC;IACtE;EACF,CAAC;EAED,MAAMyB,UAAU,GAAIzB,EAAE,IAAK;IACzB,MAAMkB,SAAS,GAAGhC,UAAU,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3B,EAAE,KAAKA,EAAE,CAAC;IACnD,IAAIkB,SAAS,EAAE;MACb3B,WAAW,CAAC;QACVC,IAAI,EAAE0B,SAAS,CAAC1B,IAAI;QACpBI,WAAW,EAAEsB,SAAS,CAACtB,WAAW;QAClCG,WAAW,EAAEmB,SAAS,CAACnB,WAAW;QAClCF,KAAK,EAAEqB,SAAS,CAACrB,KAAK,CAAC+B,QAAQ,CAAC,CAAC;QACjC9B,MAAM,EAAEoB,SAAS,CAACpB,MAAM,CAAC8B,QAAQ,CAAC;MACpC,CAAC,CAAC;MACFC,YAAY,CAAC7B,EAAE,CAAC;MAChBX,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC;EAED,oBACEN,OAAA;IAAK+C,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAEjChD,OAAA;MAAK+C,SAAS,EAAC,YAAY;MAACE,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBAC1DhD,OAAA;QAAK+C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBhD,OAAA;UAAK+C,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrDnD,cAAc,CAACmC,aAAa,CAAC,CAAC;QAAC;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACNvD,OAAA;UAAK+C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eAENvD,OAAA;QAAK+C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBhD,OAAA;UAAK+C,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrDnD,cAAc,CAACuC,cAAc,CAAC,CAAC;QAAC;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACNvD,OAAA;UAAK+C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eAENvD,OAAA;QAAK+C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBhD,OAAA;UAAK+C,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrDnD,cAAc,CAACwC,iBAAiB,CAAC,CAAC;QAAC;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACNvD,OAAA;UAAK+C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvD,OAAA;MAAK+C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBhD,OAAA;QAAK+C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BhD,OAAA;UAAI+C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1CvD,OAAA;UACE+C,SAAS,EAAC,iBAAiB;UAC3BS,OAAO,EAAEA,CAAA,KAAMlD,WAAW,CAAC,CAACD,QAAQ,CAAE;UAAA2C,QAAA,EACvC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLlD,QAAQ,iBACPL,OAAA;QAAMyD,QAAQ,EAAEhC,YAAa;QAACwB,KAAK,EAAE;UAAEC,YAAY,EAAE,MAAM;UAAEQ,OAAO,EAAE,MAAM;UAAEC,eAAe,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAZ,QAAA,gBAC9HhD,OAAA;UAAK+C,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhD,OAAA;YAAK+C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhD,OAAA;cAAO+C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CvD,OAAA;cACE6D,IAAI,EAAC,MAAM;cACXxC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEf,QAAQ,CAACE,IAAK;cACrBqD,QAAQ,EAAE3C,iBAAkB;cAC5B4B,SAAS,EAAC,YAAY;cACtBgB,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENvD,OAAA;YAAK+C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhD,OAAA;cAAO+C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDvD,OAAA;cACEqB,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAEf,QAAQ,CAACS,WAAY;cAC5B8C,QAAQ,EAAE3C,iBAAkB;cAC5B4B,SAAS,EAAC,YAAY;cACtBgB,QAAQ;cAAAf,QAAA,gBAERhD,OAAA;gBAAQsB,KAAK,EAAC,oBAAK;gBAAA0B,QAAA,EAAC;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCvD,OAAA;gBAAQsB,KAAK,EAAC,gCAAO;gBAAA0B,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCvD,OAAA;gBAAQsB,KAAK,EAAC,0BAAM;gBAAA0B,QAAA,EAAC;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCvD,OAAA;gBAAQsB,KAAK,EAAC,oBAAK;gBAAA0B,QAAA,EAAC;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCvD,OAAA;gBAAQsB,KAAK,EAAC,gCAAO;gBAAA0B,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCvD,OAAA;gBAAQsB,KAAK,EAAC,0BAAM;gBAAA0B,QAAA,EAAC;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCvD,OAAA;gBAAQsB,KAAK,EAAC,0BAAM;gBAAA0B,QAAA,EAAC;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvD,OAAA;UAAK+C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhD,OAAA;YAAO+C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3CvD,OAAA;YACE6D,IAAI,EAAC,MAAM;YACXxC,IAAI,EAAC,aAAa;YAClBC,KAAK,EAAEf,QAAQ,CAACM,WAAY;YAC5BiD,QAAQ,EAAE3C,iBAAkB;YAC5B4B,SAAS,EAAC,YAAY;YACtBiB,WAAW,EAAC,qEAAc;YAC1BD,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvD,OAAA;UAAK+C,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhD,OAAA;YAAK+C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhD,OAAA;cAAO+C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CvD,OAAA;cACE6D,IAAI,EAAC,QAAQ;cACbxC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEf,QAAQ,CAACO,KAAM;cACtBgD,QAAQ,EAAE3C,iBAAkB;cAC5B4B,SAAS,EAAC,YAAY;cACtBiB,WAAW,EAAC,MAAM;cAClBC,IAAI,EAAC;YAAM;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENvD,OAAA;YAAK+C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhD,OAAA;cAAO+C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CvD,OAAA;cACE6D,IAAI,EAAC,QAAQ;cACbxC,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAEf,QAAQ,CAACQ,MAAO;cACvB+C,QAAQ,EAAE3C,iBAAkB;cAC5B4B,SAAS,EAAC,YAAY;cACtBiB,WAAW,EAAC,MAAM;cAClBC,IAAI,EAAC;YAAM;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvD,OAAA;UAAKiD,KAAK,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAnB,QAAA,gBAC3ChD,OAAA;YAAQ6D,IAAI,EAAC,QAAQ;YAACd,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAElD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvD,OAAA;YACE6D,IAAI,EAAC,QAAQ;YACbd,SAAS,EAAC,mBAAmB;YAC7BS,OAAO,EAAEA,CAAA,KAAMlD,WAAW,CAAC,KAAK,CAAE;YAAA0C,QAAA,EACnC;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP,eAGDvD,OAAA;QAAK+C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BhD,OAAA;UAAO+C,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtBhD,OAAA;YAAAgD,QAAA,eACEhD,OAAA;cAAAgD,QAAA,gBACEhD,OAAA;gBAAAgD,QAAA,EAAI;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBvD,OAAA;gBAAAgD,QAAA,EAAI;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdvD,OAAA;gBAAAgD,QAAA,EAAI;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBvD,OAAA;gBAAAgD,QAAA,EAAI;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbvD,OAAA;gBAAAgD,QAAA,EAAI;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbvD,OAAA;gBAAAgD,QAAA,EAAI;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfvD,OAAA;gBAAAgD,QAAA,EAAI;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRvD,OAAA;YAAAgD,QAAA,EACG7C,UAAU,CAACiE,GAAG,CAAEjC,SAAS,iBACxBnC,OAAA;cAAAgD,QAAA,gBACEhD,OAAA;gBAAAgD,QAAA,EAAKlD,UAAU,CAACqC,SAAS,CAAC1B,IAAI;cAAC;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrCvD,OAAA;gBAAAgD,QAAA,EAAKb,SAAS,CAACtB;cAAW;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChCvD,OAAA;gBAAAgD,QAAA,eACEhD,OAAA;kBAAM+C,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEb,SAAS,CAACnB;gBAAW;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACLvD,OAAA;gBAAIiD,KAAK,EAAE;kBAAEE,KAAK,EAAEhB,SAAS,CAACrB,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG;gBAAO,CAAE;gBAAAkC,QAAA,EAC5Db,SAAS,CAACrB,KAAK,GAAG,CAAC,GAAGjB,cAAc,CAACsC,SAAS,CAACrB,KAAK,CAAC,GAAG;cAAG;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACLvD,OAAA;gBAAIiD,KAAK,EAAE;kBAAEE,KAAK,EAAEhB,SAAS,CAACpB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;gBAAO,CAAE;gBAAAiC,QAAA,EAC7Db,SAAS,CAACpB,MAAM,GAAG,CAAC,GAAGlB,cAAc,CAACsC,SAAS,CAACpB,MAAM,CAAC,GAAG;cAAG;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACLvD,OAAA;gBAAIiD,KAAK,EAAE;kBACToB,UAAU,EAAE,MAAM;kBAClBlB,KAAK,EAAEhB,SAAS,CAACjB,OAAO,IAAI,CAAC,GAAG,SAAS,GAAG;gBAC9C,CAAE;gBAAA8B,QAAA,EACCnD,cAAc,CAACsC,SAAS,CAACjB,OAAO;cAAC;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACLvD,OAAA;gBAAAgD,QAAA,eACEhD,OAAA;kBAAQ+C,SAAS,EAAC,mBAAmB;kBAACE,KAAK,EAAE;oBAAES,OAAO,EAAE,UAAU;oBAAEY,QAAQ,EAAE;kBAAO,CAAE;kBAAAtB,QAAA,EAAC;gBAExF;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAtBEpB,SAAS,CAAClB,EAAE;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBjB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CAtSID,iBAAiB;AAAAsE,EAAA,GAAjBtE,iBAAiB;AAwSvB,eAAeA,iBAAiB;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}