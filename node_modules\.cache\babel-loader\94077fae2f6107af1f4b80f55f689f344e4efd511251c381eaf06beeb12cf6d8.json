{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\AccountStatements.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { formatCurrency } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AccountStatements = () => {\n  _s();\n  const [statements, setStatements] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    description: '',\n    debit: '',\n    credit: '',\n    accountType: 'عام'\n  });\n  useEffect(() => {\n    // جلب البيانات من قاعدة البيانات\n    // مؤقتاً سنستخدم بيانات تجريبية\n    setStatements([{\n      id: 1,\n      date: '2024-01-15',\n      description: 'إيداع نقدي',\n      debit: 0,\n      credit: 25000,\n      balance: 25000,\n      accountType: 'خزينة'\n    }, {\n      id: 2,\n      date: '2024-01-14',\n      description: 'مشتريات مواد خام',\n      debit: 8500,\n      credit: 0,\n      balance: 16500,\n      accountType: 'مخزن'\n    }, {\n      id: 3,\n      date: '2024-01-13',\n      description: 'مصروفات نقل',\n      debit: 3200,\n      credit: 0,\n      balance: 13300,\n      accountType: 'نقل'\n    }]);\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    const newStatement = {\n      id: statements.length + 1,\n      ...formData,\n      debit: parseFloat(formData.debit) || 0,\n      credit: parseFloat(formData.credit) || 0,\n      balance: calculateNewBalance()\n    };\n    setStatements(prev => [newStatement, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      description: '',\n      debit: '',\n      credit: '',\n      accountType: 'عام'\n    });\n    setShowForm(false);\n  };\n  const calculateNewBalance = () => {\n    const lastBalance = statements.length > 0 ? statements[0].balance : 0;\n    const debit = parseFloat(formData.debit) || 0;\n    const credit = parseFloat(formData.credit) || 0;\n    return lastBalance + credit - debit;\n  };\n  const getTotalDebit = () => {\n    return statements.reduce((sum, statement) => sum + statement.debit, 0);\n  };\n  const getTotalCredit = () => {\n    return statements.reduce((sum, statement) => sum + statement.credit, 0);\n  };\n  const getCurrentBalance = () => {\n    return getTotalCredit() - getTotalDebit();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"account-statements\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#dc3545'\n          },\n          children: formatCurrency(getTotalDebit())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u062F\\u064A\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#ffa500'\n          },\n          children: formatCurrency(getTotalCredit())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u062F\\u0627\\u0626\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#1e3a8a'\n          },\n          children: formatCurrency(getCurrentBalance())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0643\\u0634\\u0641 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => setShowForm(!showForm),\n          children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0642\\u064A\\u062F \\u062C\\u062F\\u064A\\u062F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), showForm && /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        style: {\n          marginBottom: '20px',\n          padding: '20px',\n          backgroundColor: '#f8f9fa',\n          borderRadius: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              name: \"date\",\n              value: formData.date,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"accountType\",\n              value: formData.accountType,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0639\\u0627\\u0645\",\n                children: \"\\u0639\\u0627\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u062E\\u0632\\u064A\\u0646\\u0629\",\n                children: \"\\u062E\\u0632\\u064A\\u0646\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0645\\u062E\\u0632\\u0646\",\n                children: \"\\u0645\\u062E\\u0632\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0646\\u0642\\u0644\",\n                children: \"\\u0646\\u0642\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0645\\u0639\\u064A\\u0634\\u0629\",\n                children: \"\\u0645\\u0639\\u064A\\u0634\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0637\\u0644\\u0627\\u0621\",\n                children: \"\\u0637\\u0644\\u0627\\u0621\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0645\\u0635\\u0646\\u0639\",\n                children: \"\\u0645\\u0635\\u0646\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"description\",\n            value: formData.description,\n            onChange: handleInputChange,\n            className: \"form-input\",\n            placeholder: \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0645\\u0639\\u0627\\u0645\\u0644\\u0629\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0645\\u062F\\u064A\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"debit\",\n              value: formData.debit,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"0.00\",\n              step: \"0.01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u062F\\u0627\\u0626\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"credit\",\n              value: formData.credit,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"0.00\",\n              step: \"0.01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: \"\\uD83D\\uDCBE \\u062D\\u0641\\u0638 \\u0627\\u0644\\u0642\\u064A\\u062F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn btn-secondary\",\n            onClick: () => setShowForm(false),\n            children: \"\\u274C \\u0625\\u0644\\u063A\\u0627\\u0621\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0645\\u062F\\u064A\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u062F\\u0627\\u0626\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: statements.map(statement => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(statement.date).toLocaleDateString('ar-SA')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: statement.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge badge-info\",\n                  children: statement.accountType\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  color: statement.debit > 0 ? '#dc3545' : '#666'\n                },\n                children: statement.debit > 0 ? formatCurrency(statement.debit) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  color: statement.credit > 0 ? '#ffa500' : '#666'\n                },\n                children: statement.credit > 0 ? formatCurrency(statement.credit) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  fontWeight: 'bold',\n                  color: statement.balance >= 0 ? '#1e3a8a' : '#dc3545'\n                },\n                children: formatCurrency(statement.balance)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '5px 10px',\n                    fontSize: '12px'\n                  },\n                  children: \"\\u270F\\uFE0F \\u062A\\u0639\\u062F\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this)]\n            }, statement.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountStatements, \"7bAKIV1eHvpDzANl8d6G50mA5Qw=\");\n_c = AccountStatements;\nexport default AccountStatements;\nvar _c;\n$RefreshReg$(_c, \"AccountStatements\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "formatCurrency", "jsxDEV", "_jsxDEV", "AccountStatements", "_s", "statements", "setStatements", "showForm", "setShowForm", "formData", "setFormData", "date", "Date", "toISOString", "split", "description", "debit", "credit", "accountType", "id", "balance", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "newStatement", "length", "parseFloat", "calculateNewBalance", "lastBalance", "getTotalDebit", "reduce", "sum", "statement", "getTotalCredit", "getCurrentBalance", "className", "children", "style", "marginBottom", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "padding", "backgroundColor", "borderRadius", "type", "onChange", "required", "placeholder", "step", "display", "gap", "map", "toLocaleDateString", "fontWeight", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/AccountStatements.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { formatCurrency } from '../utils/currency';\n\nconst AccountStatements = () => {\n  const [statements, setStatements] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    description: '',\n    debit: '',\n    credit: '',\n    accountType: 'عام'\n  });\n\n  useEffect(() => {\n    // جلب البيانات من قاعدة البيانات\n    // مؤقتاً سنستخدم بيانات تجريبية\n    setStatements([\n      {\n        id: 1,\n        date: '2024-01-15',\n        description: 'إيداع نقدي',\n        debit: 0,\n        credit: 25000,\n        balance: 25000,\n        accountType: 'خزينة'\n      },\n      {\n        id: 2,\n        date: '2024-01-14',\n        description: 'مشتريات مواد خام',\n        debit: 8500,\n        credit: 0,\n        balance: 16500,\n        accountType: 'مخزن'\n      },\n      {\n        id: 3,\n        date: '2024-01-13',\n        description: 'مصروفات نقل',\n        debit: 3200,\n        credit: 0,\n        balance: 13300,\n        accountType: 'نقل'\n      }\n    ]);\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    const newStatement = {\n      id: statements.length + 1,\n      ...formData,\n      debit: parseFloat(formData.debit) || 0,\n      credit: parseFloat(formData.credit) || 0,\n      balance: calculateNewBalance()\n    };\n\n    setStatements(prev => [newStatement, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      description: '',\n      debit: '',\n      credit: '',\n      accountType: 'عام'\n    });\n    setShowForm(false);\n  };\n\n  const calculateNewBalance = () => {\n    const lastBalance = statements.length > 0 ? statements[0].balance : 0;\n    const debit = parseFloat(formData.debit) || 0;\n    const credit = parseFloat(formData.credit) || 0;\n    return lastBalance + credit - debit;\n  };\n\n\n\n  const getTotalDebit = () => {\n    return statements.reduce((sum, statement) => sum + statement.debit, 0);\n  };\n\n  const getTotalCredit = () => {\n    return statements.reduce((sum, statement) => sum + statement.credit, 0);\n  };\n\n  const getCurrentBalance = () => {\n    return getTotalCredit() - getTotalDebit();\n  };\n\n  return (\n    <div className=\"account-statements\">\n      {/* ملخص الحساب */}\n      <div className=\"stats-grid\" style={{ marginBottom: '30px' }}>\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#dc3545' }}>\n            {formatCurrency(getTotalDebit())}\n          </div>\n          <div className=\"stat-label\">إجمالي المدين</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#ffa500' }}>\n            {formatCurrency(getTotalCredit())}\n          </div>\n          <div className=\"stat-label\">إجمالي الدائن</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#1e3a8a' }}>\n            {formatCurrency(getCurrentBalance())}\n          </div>\n          <div className=\"stat-label\">الرصيد الحالي</div>\n        </div>\n      </div>\n\n      {/* كشف الحساب */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">كشف الحساب</h3>\n          <button \n            className=\"btn btn-primary\"\n            onClick={() => setShowForm(!showForm)}\n          >\n            ➕ إضافة قيد جديد\n          </button>\n        </div>\n\n        {/* نموذج إضافة قيد */}\n        {showForm && (\n          <form onSubmit={handleSubmit} style={{ marginBottom: '20px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">التاريخ</label>\n                <input\n                  type=\"date\"\n                  name=\"date\"\n                  value={formData.date}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  required\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">نوع الحساب</label>\n                <select\n                  name=\"accountType\"\n                  value={formData.accountType}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  required\n                >\n                  <option value=\"عام\">عام</option>\n                  <option value=\"خزينة\">خزينة</option>\n                  <option value=\"مخزن\">مخزن</option>\n                  <option value=\"نقل\">نقل</option>\n                  <option value=\"معيشة\">معيشة</option>\n                  <option value=\"طلاء\">طلاء</option>\n                  <option value=\"مصنع\">مصنع</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">الوصف</label>\n              <input\n                type=\"text\"\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"وصف المعاملة\"\n                required\n              />\n            </div>\n\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">مدين</label>\n                <input\n                  type=\"number\"\n                  name=\"debit\"\n                  value={formData.debit}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"0.00\"\n                  step=\"0.01\"\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">دائن</label>\n                <input\n                  type=\"number\"\n                  name=\"credit\"\n                  value={formData.credit}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"0.00\"\n                  step=\"0.01\"\n                />\n              </div>\n            </div>\n\n            <div style={{ display: 'flex', gap: '10px' }}>\n              <button type=\"submit\" className=\"btn btn-primary\">\n                💾 حفظ القيد\n              </button>\n              <button \n                type=\"button\" \n                className=\"btn btn-secondary\"\n                onClick={() => setShowForm(false)}\n              >\n                ❌ إلغاء\n              </button>\n            </div>\n          </form>\n        )}\n\n        {/* جدول كشف الحساب */}\n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>التاريخ</th>\n                <th>الوصف</th>\n                <th>نوع الحساب</th>\n                <th>مدين</th>\n                <th>دائن</th>\n                <th>الرصيد</th>\n                <th>إجراءات</th>\n              </tr>\n            </thead>\n            <tbody>\n              {statements.map((statement) => (\n                <tr key={statement.id}>\n                  <td>{new Date(statement.date).toLocaleDateString('ar-SA')}</td>\n                  <td>{statement.description}</td>\n                  <td>\n                    <span className=\"badge badge-info\">{statement.accountType}</span>\n                  </td>\n                  <td style={{ color: statement.debit > 0 ? '#dc3545' : '#666' }}>\n                    {statement.debit > 0 ? formatCurrency(statement.debit) : '-'}\n                  </td>\n                  <td style={{ color: statement.credit > 0 ? '#ffa500' : '#666' }}>\n                    {statement.credit > 0 ? formatCurrency(statement.credit) : '-'}\n                  </td>\n                  <td style={{\n                    fontWeight: 'bold',\n                    color: statement.balance >= 0 ? '#1e3a8a' : '#dc3545'\n                  }}>\n                    {formatCurrency(statement.balance)}\n                  </td>\n                  <td>\n                    <button className=\"btn btn-secondary\" style={{ padding: '5px 10px', fontSize: '12px' }}>\n                      ✏️ تعديل\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AccountStatements;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC;IACvCa,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5CC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;EACf,CAAC,CAAC;EAEFnB,SAAS,CAAC,MAAM;IACd;IACA;IACAO,aAAa,CAAC,CACZ;MACEa,EAAE,EAAE,CAAC;MACLR,IAAI,EAAE,YAAY;MAClBI,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,KAAK;MACbG,OAAO,EAAE,KAAK;MACdF,WAAW,EAAE;IACf,CAAC,EACD;MACEC,EAAE,EAAE,CAAC;MACLR,IAAI,EAAE,YAAY;MAClBI,WAAW,EAAE,kBAAkB;MAC/BC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,CAAC;MACTG,OAAO,EAAE,KAAK;MACdF,WAAW,EAAE;IACf,CAAC,EACD;MACEC,EAAE,EAAE,CAAC;MACLR,IAAI,EAAE,YAAY;MAClBI,WAAW,EAAE,aAAa;MAC1BC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,CAAC;MACTG,OAAO,EAAE,KAAK;MACdF,WAAW,EAAE;IACf,CAAC,CACF,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCf,WAAW,CAACgB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAIL,CAAC,IAAK;IAC1BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,MAAMC,YAAY,GAAG;MACnBV,EAAE,EAAEd,UAAU,CAACyB,MAAM,GAAG,CAAC;MACzB,GAAGrB,QAAQ;MACXO,KAAK,EAAEe,UAAU,CAACtB,QAAQ,CAACO,KAAK,CAAC,IAAI,CAAC;MACtCC,MAAM,EAAEc,UAAU,CAACtB,QAAQ,CAACQ,MAAM,CAAC,IAAI,CAAC;MACxCG,OAAO,EAAEY,mBAAmB,CAAC;IAC/B,CAAC;IAED1B,aAAa,CAACoB,IAAI,IAAI,CAACG,YAAY,EAAE,GAAGH,IAAI,CAAC,CAAC;IAC9ChB,WAAW,CAAC;MACVC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE;IACf,CAAC,CAAC;IACFV,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMwB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,WAAW,GAAG5B,UAAU,CAACyB,MAAM,GAAG,CAAC,GAAGzB,UAAU,CAAC,CAAC,CAAC,CAACe,OAAO,GAAG,CAAC;IACrE,MAAMJ,KAAK,GAAGe,UAAU,CAACtB,QAAQ,CAACO,KAAK,CAAC,IAAI,CAAC;IAC7C,MAAMC,MAAM,GAAGc,UAAU,CAACtB,QAAQ,CAACQ,MAAM,CAAC,IAAI,CAAC;IAC/C,OAAOgB,WAAW,GAAGhB,MAAM,GAAGD,KAAK;EACrC,CAAC;EAID,MAAMkB,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAO7B,UAAU,CAAC8B,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAKD,GAAG,GAAGC,SAAS,CAACrB,KAAK,EAAE,CAAC,CAAC;EACxE,CAAC;EAED,MAAMsB,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOjC,UAAU,CAAC8B,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAKD,GAAG,GAAGC,SAAS,CAACpB,MAAM,EAAE,CAAC,CAAC;EACzE,CAAC;EAED,MAAMsB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAOD,cAAc,CAAC,CAAC,GAAGJ,aAAa,CAAC,CAAC;EAC3C,CAAC;EAED,oBACEhC,OAAA;IAAKsC,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAEjCvC,OAAA;MAAKsC,SAAS,EAAC,YAAY;MAACE,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBAC1DvC,OAAA;QAAKsC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBvC,OAAA;UAAKsC,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrDzC,cAAc,CAACkC,aAAa,CAAC,CAAC;QAAC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACN9C,OAAA;UAAKsC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eAEN9C,OAAA;QAAKsC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBvC,OAAA;UAAKsC,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrDzC,cAAc,CAACsC,cAAc,CAAC,CAAC;QAAC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACN9C,OAAA;UAAKsC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eAEN9C,OAAA;QAAKsC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBvC,OAAA;UAAKsC,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrDzC,cAAc,CAACuC,iBAAiB,CAAC,CAAC;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACN9C,OAAA;UAAKsC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9C,OAAA;MAAKsC,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBvC,OAAA;QAAKsC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvC,OAAA;UAAIsC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1C9C,OAAA;UACEsC,SAAS,EAAC,iBAAiB;UAC3BS,OAAO,EAAEA,CAAA,KAAMzC,WAAW,CAAC,CAACD,QAAQ,CAAE;UAAAkC,QAAA,EACvC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLzC,QAAQ,iBACPL,OAAA;QAAMgD,QAAQ,EAAEvB,YAAa;QAACe,KAAK,EAAE;UAAEC,YAAY,EAAE,MAAM;UAAEQ,OAAO,EAAE,MAAM;UAAEC,eAAe,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAZ,QAAA,gBAC9HvC,OAAA;UAAKsC,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBvC,OAAA;YAAKsC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvC,OAAA;cAAOsC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7C9C,OAAA;cACEoD,IAAI,EAAC,MAAM;cACX/B,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEf,QAAQ,CAACE,IAAK;cACrB4C,QAAQ,EAAElC,iBAAkB;cAC5BmB,SAAS,EAAC,YAAY;cACtBgB,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9C,OAAA;YAAKsC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvC,OAAA;cAAOsC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChD9C,OAAA;cACEqB,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAEf,QAAQ,CAACS,WAAY;cAC5BqC,QAAQ,EAAElC,iBAAkB;cAC5BmB,SAAS,EAAC,YAAY;cACtBgB,QAAQ;cAAAf,QAAA,gBAERvC,OAAA;gBAAQsB,KAAK,EAAC,oBAAK;gBAAAiB,QAAA,EAAC;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC9C,OAAA;gBAAQsB,KAAK,EAAC,gCAAO;gBAAAiB,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC9C,OAAA;gBAAQsB,KAAK,EAAC,0BAAM;gBAAAiB,QAAA,EAAC;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClC9C,OAAA;gBAAQsB,KAAK,EAAC,oBAAK;gBAAAiB,QAAA,EAAC;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC9C,OAAA;gBAAQsB,KAAK,EAAC,gCAAO;gBAAAiB,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC9C,OAAA;gBAAQsB,KAAK,EAAC,0BAAM;gBAAAiB,QAAA,EAAC;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClC9C,OAAA;gBAAQsB,KAAK,EAAC,0BAAM;gBAAAiB,QAAA,EAAC;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9C,OAAA;UAAKsC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvC,OAAA;YAAOsC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3C9C,OAAA;YACEoD,IAAI,EAAC,MAAM;YACX/B,IAAI,EAAC,aAAa;YAClBC,KAAK,EAAEf,QAAQ,CAACM,WAAY;YAC5BwC,QAAQ,EAAElC,iBAAkB;YAC5BmB,SAAS,EAAC,YAAY;YACtBiB,WAAW,EAAC,qEAAc;YAC1BD,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN9C,OAAA;UAAKsC,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBvC,OAAA;YAAKsC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvC,OAAA;cAAOsC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1C9C,OAAA;cACEoD,IAAI,EAAC,QAAQ;cACb/B,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEf,QAAQ,CAACO,KAAM;cACtBuC,QAAQ,EAAElC,iBAAkB;cAC5BmB,SAAS,EAAC,YAAY;cACtBiB,WAAW,EAAC,MAAM;cAClBC,IAAI,EAAC;YAAM;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9C,OAAA;YAAKsC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvC,OAAA;cAAOsC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1C9C,OAAA;cACEoD,IAAI,EAAC,QAAQ;cACb/B,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAEf,QAAQ,CAACQ,MAAO;cACvBsC,QAAQ,EAAElC,iBAAkB;cAC5BmB,SAAS,EAAC,YAAY;cACtBiB,WAAW,EAAC,MAAM;cAClBC,IAAI,EAAC;YAAM;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9C,OAAA;UAAKwC,KAAK,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAnB,QAAA,gBAC3CvC,OAAA;YAAQoD,IAAI,EAAC,QAAQ;YAACd,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAElD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9C,OAAA;YACEoD,IAAI,EAAC,QAAQ;YACbd,SAAS,EAAC,mBAAmB;YAC7BS,OAAO,EAAEA,CAAA,KAAMzC,WAAW,CAAC,KAAK,CAAE;YAAAiC,QAAA,EACnC;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP,eAGD9C,OAAA;QAAKsC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BvC,OAAA;UAAOsC,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtBvC,OAAA;YAAAuC,QAAA,eACEvC,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAAuC,QAAA,EAAI;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB9C,OAAA;gBAAAuC,QAAA,EAAI;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACd9C,OAAA;gBAAAuC,QAAA,EAAI;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnB9C,OAAA;gBAAAuC,QAAA,EAAI;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb9C,OAAA;gBAAAuC,QAAA,EAAI;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb9C,OAAA;gBAAAuC,QAAA,EAAI;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf9C,OAAA;gBAAAuC,QAAA,EAAI;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR9C,OAAA;YAAAuC,QAAA,EACGpC,UAAU,CAACwD,GAAG,CAAExB,SAAS,iBACxBnC,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAAuC,QAAA,EAAK,IAAI7B,IAAI,CAACyB,SAAS,CAAC1B,IAAI,CAAC,CAACmD,kBAAkB,CAAC,OAAO;cAAC;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/D9C,OAAA;gBAAAuC,QAAA,EAAKJ,SAAS,CAACtB;cAAW;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChC9C,OAAA;gBAAAuC,QAAA,eACEvC,OAAA;kBAAMsC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEJ,SAAS,CAACnB;gBAAW;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACL9C,OAAA;gBAAIwC,KAAK,EAAE;kBAAEE,KAAK,EAAEP,SAAS,CAACrB,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG;gBAAO,CAAE;gBAAAyB,QAAA,EAC5DJ,SAAS,CAACrB,KAAK,GAAG,CAAC,GAAGhB,cAAc,CAACqC,SAAS,CAACrB,KAAK,CAAC,GAAG;cAAG;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACL9C,OAAA;gBAAIwC,KAAK,EAAE;kBAAEE,KAAK,EAAEP,SAAS,CAACpB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;gBAAO,CAAE;gBAAAwB,QAAA,EAC7DJ,SAAS,CAACpB,MAAM,GAAG,CAAC,GAAGjB,cAAc,CAACqC,SAAS,CAACpB,MAAM,CAAC,GAAG;cAAG;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACL9C,OAAA;gBAAIwC,KAAK,EAAE;kBACTqB,UAAU,EAAE,MAAM;kBAClBnB,KAAK,EAAEP,SAAS,CAACjB,OAAO,IAAI,CAAC,GAAG,SAAS,GAAG;gBAC9C,CAAE;gBAAAqB,QAAA,EACCzC,cAAc,CAACqC,SAAS,CAACjB,OAAO;cAAC;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACL9C,OAAA;gBAAAuC,QAAA,eACEvC,OAAA;kBAAQsC,SAAS,EAAC,mBAAmB;kBAACE,KAAK,EAAE;oBAAES,OAAO,EAAE,UAAU;oBAAEa,QAAQ,EAAE;kBAAO,CAAE;kBAAAvB,QAAA,EAAC;gBAExF;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAtBEX,SAAS,CAAClB,EAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBjB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5C,EAAA,CAjRID,iBAAiB;AAAA8D,EAAA,GAAjB9D,iBAAiB;AAmRvB,eAAeA,iBAAiB;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}