{"ast": null, "code": "'use strict';\n\nvar utils = require('./utils');\nvar has = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\nvar defaults = {\n  allowDots: false,\n  allowEmptyArrays: false,\n  allowPrototypes: false,\n  allowSparse: false,\n  arrayLimit: 20,\n  charset: 'utf-8',\n  charsetSentinel: false,\n  comma: false,\n  decodeDotInKeys: false,\n  decoder: utils.decode,\n  delimiter: '&',\n  depth: 5,\n  duplicates: 'combine',\n  ignoreQueryPrefix: false,\n  interpretNumericEntities: false,\n  parameterLimit: 1000,\n  parseArrays: true,\n  plainObjects: false,\n  strictDepth: false,\n  strictNullHandling: false,\n  throwOnLimitExceeded: false\n};\nvar interpretNumericEntities = function (str) {\n  return str.replace(/&#(\\d+);/g, function ($0, numberStr) {\n    return String.fromCharCode(parseInt(numberStr, 10));\n  });\n};\nvar parseArrayValue = function (val, options, currentArrayLength) {\n  if (val && typeof val === 'string' && options.comma && val.indexOf(',') > -1) {\n    return val.split(',');\n  }\n  if (options.throwOnLimitExceeded && currentArrayLength >= options.arrayLimit) {\n    throw new RangeError('Array limit exceeded. Only ' + options.arrayLimit + ' element' + (options.arrayLimit === 1 ? '' : 's') + ' allowed in an array.');\n  }\n  return val;\n};\n\n// This is what browsers will submit when the ✓ character occurs in an\n// application/x-www-form-urlencoded body and the encoding of the page containing\n// the form is iso-8859-1, or when the submitted form has an accept-charset\n// attribute of iso-8859-1. Presumably also with other charsets that do not contain\n// the ✓ character, such as us-ascii.\nvar isoSentinel = 'utf8=%26%2310003%3B'; // encodeURIComponent('&#10003;')\n\n// These are the percent-encoded utf-8 octets representing a checkmark, indicating that the request actually is utf-8 encoded.\nvar charsetSentinel = 'utf8=%E2%9C%93'; // encodeURIComponent('✓')\n\nvar parseValues = function parseQueryStringValues(str, options) {\n  var obj = {\n    __proto__: null\n  };\n  var cleanStr = options.ignoreQueryPrefix ? str.replace(/^\\?/, '') : str;\n  cleanStr = cleanStr.replace(/%5B/gi, '[').replace(/%5D/gi, ']');\n  var limit = options.parameterLimit === Infinity ? undefined : options.parameterLimit;\n  var parts = cleanStr.split(options.delimiter, options.throwOnLimitExceeded ? limit + 1 : limit);\n  if (options.throwOnLimitExceeded && parts.length > limit) {\n    throw new RangeError('Parameter limit exceeded. Only ' + limit + ' parameter' + (limit === 1 ? '' : 's') + ' allowed.');\n  }\n  var skipIndex = -1; // Keep track of where the utf8 sentinel was found\n  var i;\n  var charset = options.charset;\n  if (options.charsetSentinel) {\n    for (i = 0; i < parts.length; ++i) {\n      if (parts[i].indexOf('utf8=') === 0) {\n        if (parts[i] === charsetSentinel) {\n          charset = 'utf-8';\n        } else if (parts[i] === isoSentinel) {\n          charset = 'iso-8859-1';\n        }\n        skipIndex = i;\n        i = parts.length; // The eslint settings do not allow break;\n      }\n    }\n  }\n  for (i = 0; i < parts.length; ++i) {\n    if (i === skipIndex) {\n      continue;\n    }\n    var part = parts[i];\n    var bracketEqualsPos = part.indexOf(']=');\n    var pos = bracketEqualsPos === -1 ? part.indexOf('=') : bracketEqualsPos + 1;\n    var key;\n    var val;\n    if (pos === -1) {\n      key = options.decoder(part, defaults.decoder, charset, 'key');\n      val = options.strictNullHandling ? null : '';\n    } else {\n      key = options.decoder(part.slice(0, pos), defaults.decoder, charset, 'key');\n      val = utils.maybeMap(parseArrayValue(part.slice(pos + 1), options, isArray(obj[key]) ? obj[key].length : 0), function (encodedVal) {\n        return options.decoder(encodedVal, defaults.decoder, charset, 'value');\n      });\n    }\n    if (val && options.interpretNumericEntities && charset === 'iso-8859-1') {\n      val = interpretNumericEntities(String(val));\n    }\n    if (part.indexOf('[]=') > -1) {\n      val = isArray(val) ? [val] : val;\n    }\n    var existing = has.call(obj, key);\n    if (existing && options.duplicates === 'combine') {\n      obj[key] = utils.combine(obj[key], val);\n    } else if (!existing || options.duplicates === 'last') {\n      obj[key] = val;\n    }\n  }\n  return obj;\n};\nvar parseObject = function (chain, val, options, valuesParsed) {\n  var currentArrayLength = 0;\n  if (chain.length > 0 && chain[chain.length - 1] === '[]') {\n    var parentKey = chain.slice(0, -1).join('');\n    currentArrayLength = Array.isArray(val) && val[parentKey] ? val[parentKey].length : 0;\n  }\n  var leaf = valuesParsed ? val : parseArrayValue(val, options, currentArrayLength);\n  for (var i = chain.length - 1; i >= 0; --i) {\n    var obj;\n    var root = chain[i];\n    if (root === '[]' && options.parseArrays) {\n      obj = options.allowEmptyArrays && (leaf === '' || options.strictNullHandling && leaf === null) ? [] : utils.combine([], leaf);\n    } else {\n      obj = options.plainObjects ? {\n        __proto__: null\n      } : {};\n      var cleanRoot = root.charAt(0) === '[' && root.charAt(root.length - 1) === ']' ? root.slice(1, -1) : root;\n      var decodedRoot = options.decodeDotInKeys ? cleanRoot.replace(/%2E/g, '.') : cleanRoot;\n      var index = parseInt(decodedRoot, 10);\n      if (!options.parseArrays && decodedRoot === '') {\n        obj = {\n          0: leaf\n        };\n      } else if (!isNaN(index) && root !== decodedRoot && String(index) === decodedRoot && index >= 0 && options.parseArrays && index <= options.arrayLimit) {\n        obj = [];\n        obj[index] = leaf;\n      } else if (decodedRoot !== '__proto__') {\n        obj[decodedRoot] = leaf;\n      }\n    }\n    leaf = obj;\n  }\n  return leaf;\n};\nvar parseKeys = function parseQueryStringKeys(givenKey, val, options, valuesParsed) {\n  if (!givenKey) {\n    return;\n  }\n\n  // Transform dot notation to bracket notation\n  var key = options.allowDots ? givenKey.replace(/\\.([^.[]+)/g, '[$1]') : givenKey;\n\n  // The regex chunks\n\n  var brackets = /(\\[[^[\\]]*])/;\n  var child = /(\\[[^[\\]]*])/g;\n\n  // Get the parent\n\n  var segment = options.depth > 0 && brackets.exec(key);\n  var parent = segment ? key.slice(0, segment.index) : key;\n\n  // Stash the parent if it exists\n\n  var keys = [];\n  if (parent) {\n    // If we aren't using plain objects, optionally prefix keys that would overwrite object prototype properties\n    if (!options.plainObjects && has.call(Object.prototype, parent)) {\n      if (!options.allowPrototypes) {\n        return;\n      }\n    }\n    keys.push(parent);\n  }\n\n  // Loop through children appending to the array until we hit depth\n\n  var i = 0;\n  while (options.depth > 0 && (segment = child.exec(key)) !== null && i < options.depth) {\n    i += 1;\n    if (!options.plainObjects && has.call(Object.prototype, segment[1].slice(1, -1))) {\n      if (!options.allowPrototypes) {\n        return;\n      }\n    }\n    keys.push(segment[1]);\n  }\n\n  // If there's a remainder, check strictDepth option for throw, else just add whatever is left\n\n  if (segment) {\n    if (options.strictDepth === true) {\n      throw new RangeError('Input depth exceeded depth option of ' + options.depth + ' and strictDepth is true');\n    }\n    keys.push('[' + key.slice(segment.index) + ']');\n  }\n  return parseObject(keys, val, options, valuesParsed);\n};\nvar normalizeParseOptions = function normalizeParseOptions(opts) {\n  if (!opts) {\n    return defaults;\n  }\n  if (typeof opts.allowEmptyArrays !== 'undefined' && typeof opts.allowEmptyArrays !== 'boolean') {\n    throw new TypeError('`allowEmptyArrays` option can only be `true` or `false`, when provided');\n  }\n  if (typeof opts.decodeDotInKeys !== 'undefined' && typeof opts.decodeDotInKeys !== 'boolean') {\n    throw new TypeError('`decodeDotInKeys` option can only be `true` or `false`, when provided');\n  }\n  if (opts.decoder !== null && typeof opts.decoder !== 'undefined' && typeof opts.decoder !== 'function') {\n    throw new TypeError('Decoder has to be a function.');\n  }\n  if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {\n    throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');\n  }\n  if (typeof opts.throwOnLimitExceeded !== 'undefined' && typeof opts.throwOnLimitExceeded !== 'boolean') {\n    throw new TypeError('`throwOnLimitExceeded` option must be a boolean');\n  }\n  var charset = typeof opts.charset === 'undefined' ? defaults.charset : opts.charset;\n  var duplicates = typeof opts.duplicates === 'undefined' ? defaults.duplicates : opts.duplicates;\n  if (duplicates !== 'combine' && duplicates !== 'first' && duplicates !== 'last') {\n    throw new TypeError('The duplicates option must be either combine, first, or last');\n  }\n  var allowDots = typeof opts.allowDots === 'undefined' ? opts.decodeDotInKeys === true ? true : defaults.allowDots : !!opts.allowDots;\n  return {\n    allowDots: allowDots,\n    allowEmptyArrays: typeof opts.allowEmptyArrays === 'boolean' ? !!opts.allowEmptyArrays : defaults.allowEmptyArrays,\n    allowPrototypes: typeof opts.allowPrototypes === 'boolean' ? opts.allowPrototypes : defaults.allowPrototypes,\n    allowSparse: typeof opts.allowSparse === 'boolean' ? opts.allowSparse : defaults.allowSparse,\n    arrayLimit: typeof opts.arrayLimit === 'number' ? opts.arrayLimit : defaults.arrayLimit,\n    charset: charset,\n    charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults.charsetSentinel,\n    comma: typeof opts.comma === 'boolean' ? opts.comma : defaults.comma,\n    decodeDotInKeys: typeof opts.decodeDotInKeys === 'boolean' ? opts.decodeDotInKeys : defaults.decodeDotInKeys,\n    decoder: typeof opts.decoder === 'function' ? opts.decoder : defaults.decoder,\n    delimiter: typeof opts.delimiter === 'string' || utils.isRegExp(opts.delimiter) ? opts.delimiter : defaults.delimiter,\n    // eslint-disable-next-line no-implicit-coercion, no-extra-parens\n    depth: typeof opts.depth === 'number' || opts.depth === false ? +opts.depth : defaults.depth,\n    duplicates: duplicates,\n    ignoreQueryPrefix: opts.ignoreQueryPrefix === true,\n    interpretNumericEntities: typeof opts.interpretNumericEntities === 'boolean' ? opts.interpretNumericEntities : defaults.interpretNumericEntities,\n    parameterLimit: typeof opts.parameterLimit === 'number' ? opts.parameterLimit : defaults.parameterLimit,\n    parseArrays: opts.parseArrays !== false,\n    plainObjects: typeof opts.plainObjects === 'boolean' ? opts.plainObjects : defaults.plainObjects,\n    strictDepth: typeof opts.strictDepth === 'boolean' ? !!opts.strictDepth : defaults.strictDepth,\n    strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults.strictNullHandling,\n    throwOnLimitExceeded: typeof opts.throwOnLimitExceeded === 'boolean' ? opts.throwOnLimitExceeded : false\n  };\n};\nmodule.exports = function (str, opts) {\n  var options = normalizeParseOptions(opts);\n  if (str === '' || str === null || typeof str === 'undefined') {\n    return options.plainObjects ? {\n      __proto__: null\n    } : {};\n  }\n  var tempObj = typeof str === 'string' ? parseValues(str, options) : str;\n  var obj = options.plainObjects ? {\n    __proto__: null\n  } : {};\n\n  // Iterate over the keys and setup the new object\n\n  var keys = Object.keys(tempObj);\n  for (var i = 0; i < keys.length; ++i) {\n    var key = keys[i];\n    var newObj = parseKeys(key, tempObj[key], options, typeof str === 'string');\n    obj = utils.merge(obj, newObj, options);\n  }\n  if (options.allowSparse === true) {\n    return obj;\n  }\n  return utils.compact(obj);\n};", "map": {"version": 3, "names": ["utils", "require", "has", "Object", "prototype", "hasOwnProperty", "isArray", "Array", "defaults", "allowDots", "allowEmptyArrays", "allowPrototypes", "allowSparse", "arrayLimit", "charset", "charset<PERSON><PERSON><PERSON>l", "comma", "decodeDotInKeys", "decoder", "decode", "delimiter", "depth", "duplicates", "ignoreQueryPrefix", "interpretNumericEntities", "parameterLimit", "parseA<PERSON>ys", "plainObjects", "strictDepth", "strict<PERSON>ull<PERSON>andling", "throwOnLimitExceeded", "str", "replace", "$0", "numberStr", "String", "fromCharCode", "parseInt", "parseArrayValue", "val", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indexOf", "split", "RangeError", "isoSentinel", "parseV<PERSON>ues", "parseQuery<PERSON>", "obj", "__proto__", "cleanStr", "limit", "Infinity", "undefined", "parts", "length", "skipIndex", "i", "part", "bracketEqualsPos", "pos", "key", "slice", "maybeMap", "encodedVal", "existing", "call", "combine", "parseObject", "chain", "valuesParsed", "parent<PERSON><PERSON>", "join", "leaf", "root", "cleanRoot", "char<PERSON>t", "decodedRoot", "index", "isNaN", "parse<PERSON>eys", "parse<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "brackets", "child", "segment", "exec", "parent", "keys", "push", "normalizeParseOptions", "opts", "TypeError", "isRegExp", "module", "exports", "tempObj", "newObj", "merge", "compact"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/node_modules/url/node_modules/qs/lib/parse.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./utils');\n\nvar has = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\n\nvar defaults = {\n    allowDots: false,\n    allowEmptyArrays: false,\n    allowPrototypes: false,\n    allowSparse: false,\n    arrayLimit: 20,\n    charset: 'utf-8',\n    charsetSentinel: false,\n    comma: false,\n    decodeDotInKeys: false,\n    decoder: utils.decode,\n    delimiter: '&',\n    depth: 5,\n    duplicates: 'combine',\n    ignoreQueryPrefix: false,\n    interpretNumericEntities: false,\n    parameterLimit: 1000,\n    parseArrays: true,\n    plainObjects: false,\n    strictDepth: false,\n    strictNullHandling: false,\n    throwOnLimitExceeded: false\n};\n\nvar interpretNumericEntities = function (str) {\n    return str.replace(/&#(\\d+);/g, function ($0, numberStr) {\n        return String.fromCharCode(parseInt(numberStr, 10));\n    });\n};\n\nvar parseArrayValue = function (val, options, currentArrayLength) {\n    if (val && typeof val === 'string' && options.comma && val.indexOf(',') > -1) {\n        return val.split(',');\n    }\n\n    if (options.throwOnLimitExceeded && currentArrayLength >= options.arrayLimit) {\n        throw new RangeError('Array limit exceeded. Only ' + options.arrayLimit + ' element' + (options.arrayLimit === 1 ? '' : 's') + ' allowed in an array.');\n    }\n\n    return val;\n};\n\n// This is what browsers will submit when the ✓ character occurs in an\n// application/x-www-form-urlencoded body and the encoding of the page containing\n// the form is iso-8859-1, or when the submitted form has an accept-charset\n// attribute of iso-8859-1. Presumably also with other charsets that do not contain\n// the ✓ character, such as us-ascii.\nvar isoSentinel = 'utf8=%26%2310003%3B'; // encodeURIComponent('&#10003;')\n\n// These are the percent-encoded utf-8 octets representing a checkmark, indicating that the request actually is utf-8 encoded.\nvar charsetSentinel = 'utf8=%E2%9C%93'; // encodeURIComponent('✓')\n\nvar parseValues = function parseQueryStringValues(str, options) {\n    var obj = { __proto__: null };\n\n    var cleanStr = options.ignoreQueryPrefix ? str.replace(/^\\?/, '') : str;\n    cleanStr = cleanStr.replace(/%5B/gi, '[').replace(/%5D/gi, ']');\n\n    var limit = options.parameterLimit === Infinity ? undefined : options.parameterLimit;\n    var parts = cleanStr.split(\n        options.delimiter,\n        options.throwOnLimitExceeded ? limit + 1 : limit\n    );\n\n    if (options.throwOnLimitExceeded && parts.length > limit) {\n        throw new RangeError('Parameter limit exceeded. Only ' + limit + ' parameter' + (limit === 1 ? '' : 's') + ' allowed.');\n    }\n\n    var skipIndex = -1; // Keep track of where the utf8 sentinel was found\n    var i;\n\n    var charset = options.charset;\n    if (options.charsetSentinel) {\n        for (i = 0; i < parts.length; ++i) {\n            if (parts[i].indexOf('utf8=') === 0) {\n                if (parts[i] === charsetSentinel) {\n                    charset = 'utf-8';\n                } else if (parts[i] === isoSentinel) {\n                    charset = 'iso-8859-1';\n                }\n                skipIndex = i;\n                i = parts.length; // The eslint settings do not allow break;\n            }\n        }\n    }\n\n    for (i = 0; i < parts.length; ++i) {\n        if (i === skipIndex) {\n            continue;\n        }\n        var part = parts[i];\n\n        var bracketEqualsPos = part.indexOf(']=');\n        var pos = bracketEqualsPos === -1 ? part.indexOf('=') : bracketEqualsPos + 1;\n\n        var key;\n        var val;\n        if (pos === -1) {\n            key = options.decoder(part, defaults.decoder, charset, 'key');\n            val = options.strictNullHandling ? null : '';\n        } else {\n            key = options.decoder(part.slice(0, pos), defaults.decoder, charset, 'key');\n\n            val = utils.maybeMap(\n                parseArrayValue(\n                    part.slice(pos + 1),\n                    options,\n                    isArray(obj[key]) ? obj[key].length : 0\n                ),\n                function (encodedVal) {\n                    return options.decoder(encodedVal, defaults.decoder, charset, 'value');\n                }\n            );\n        }\n\n        if (val && options.interpretNumericEntities && charset === 'iso-8859-1') {\n            val = interpretNumericEntities(String(val));\n        }\n\n        if (part.indexOf('[]=') > -1) {\n            val = isArray(val) ? [val] : val;\n        }\n\n        var existing = has.call(obj, key);\n        if (existing && options.duplicates === 'combine') {\n            obj[key] = utils.combine(obj[key], val);\n        } else if (!existing || options.duplicates === 'last') {\n            obj[key] = val;\n        }\n    }\n\n    return obj;\n};\n\nvar parseObject = function (chain, val, options, valuesParsed) {\n    var currentArrayLength = 0;\n    if (chain.length > 0 && chain[chain.length - 1] === '[]') {\n        var parentKey = chain.slice(0, -1).join('');\n        currentArrayLength = Array.isArray(val) && val[parentKey] ? val[parentKey].length : 0;\n    }\n\n    var leaf = valuesParsed ? val : parseArrayValue(val, options, currentArrayLength);\n\n    for (var i = chain.length - 1; i >= 0; --i) {\n        var obj;\n        var root = chain[i];\n\n        if (root === '[]' && options.parseArrays) {\n            obj = options.allowEmptyArrays && (leaf === '' || (options.strictNullHandling && leaf === null))\n                ? []\n                : utils.combine([], leaf);\n        } else {\n            obj = options.plainObjects ? { __proto__: null } : {};\n            var cleanRoot = root.charAt(0) === '[' && root.charAt(root.length - 1) === ']' ? root.slice(1, -1) : root;\n            var decodedRoot = options.decodeDotInKeys ? cleanRoot.replace(/%2E/g, '.') : cleanRoot;\n            var index = parseInt(decodedRoot, 10);\n            if (!options.parseArrays && decodedRoot === '') {\n                obj = { 0: leaf };\n            } else if (\n                !isNaN(index)\n                && root !== decodedRoot\n                && String(index) === decodedRoot\n                && index >= 0\n                && (options.parseArrays && index <= options.arrayLimit)\n            ) {\n                obj = [];\n                obj[index] = leaf;\n            } else if (decodedRoot !== '__proto__') {\n                obj[decodedRoot] = leaf;\n            }\n        }\n\n        leaf = obj;\n    }\n\n    return leaf;\n};\n\nvar parseKeys = function parseQueryStringKeys(givenKey, val, options, valuesParsed) {\n    if (!givenKey) {\n        return;\n    }\n\n    // Transform dot notation to bracket notation\n    var key = options.allowDots ? givenKey.replace(/\\.([^.[]+)/g, '[$1]') : givenKey;\n\n    // The regex chunks\n\n    var brackets = /(\\[[^[\\]]*])/;\n    var child = /(\\[[^[\\]]*])/g;\n\n    // Get the parent\n\n    var segment = options.depth > 0 && brackets.exec(key);\n    var parent = segment ? key.slice(0, segment.index) : key;\n\n    // Stash the parent if it exists\n\n    var keys = [];\n    if (parent) {\n        // If we aren't using plain objects, optionally prefix keys that would overwrite object prototype properties\n        if (!options.plainObjects && has.call(Object.prototype, parent)) {\n            if (!options.allowPrototypes) {\n                return;\n            }\n        }\n\n        keys.push(parent);\n    }\n\n    // Loop through children appending to the array until we hit depth\n\n    var i = 0;\n    while (options.depth > 0 && (segment = child.exec(key)) !== null && i < options.depth) {\n        i += 1;\n        if (!options.plainObjects && has.call(Object.prototype, segment[1].slice(1, -1))) {\n            if (!options.allowPrototypes) {\n                return;\n            }\n        }\n        keys.push(segment[1]);\n    }\n\n    // If there's a remainder, check strictDepth option for throw, else just add whatever is left\n\n    if (segment) {\n        if (options.strictDepth === true) {\n            throw new RangeError('Input depth exceeded depth option of ' + options.depth + ' and strictDepth is true');\n        }\n        keys.push('[' + key.slice(segment.index) + ']');\n    }\n\n    return parseObject(keys, val, options, valuesParsed);\n};\n\nvar normalizeParseOptions = function normalizeParseOptions(opts) {\n    if (!opts) {\n        return defaults;\n    }\n\n    if (typeof opts.allowEmptyArrays !== 'undefined' && typeof opts.allowEmptyArrays !== 'boolean') {\n        throw new TypeError('`allowEmptyArrays` option can only be `true` or `false`, when provided');\n    }\n\n    if (typeof opts.decodeDotInKeys !== 'undefined' && typeof opts.decodeDotInKeys !== 'boolean') {\n        throw new TypeError('`decodeDotInKeys` option can only be `true` or `false`, when provided');\n    }\n\n    if (opts.decoder !== null && typeof opts.decoder !== 'undefined' && typeof opts.decoder !== 'function') {\n        throw new TypeError('Decoder has to be a function.');\n    }\n\n    if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {\n        throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');\n    }\n\n    if (typeof opts.throwOnLimitExceeded !== 'undefined' && typeof opts.throwOnLimitExceeded !== 'boolean') {\n        throw new TypeError('`throwOnLimitExceeded` option must be a boolean');\n    }\n\n    var charset = typeof opts.charset === 'undefined' ? defaults.charset : opts.charset;\n\n    var duplicates = typeof opts.duplicates === 'undefined' ? defaults.duplicates : opts.duplicates;\n\n    if (duplicates !== 'combine' && duplicates !== 'first' && duplicates !== 'last') {\n        throw new TypeError('The duplicates option must be either combine, first, or last');\n    }\n\n    var allowDots = typeof opts.allowDots === 'undefined' ? opts.decodeDotInKeys === true ? true : defaults.allowDots : !!opts.allowDots;\n\n    return {\n        allowDots: allowDots,\n        allowEmptyArrays: typeof opts.allowEmptyArrays === 'boolean' ? !!opts.allowEmptyArrays : defaults.allowEmptyArrays,\n        allowPrototypes: typeof opts.allowPrototypes === 'boolean' ? opts.allowPrototypes : defaults.allowPrototypes,\n        allowSparse: typeof opts.allowSparse === 'boolean' ? opts.allowSparse : defaults.allowSparse,\n        arrayLimit: typeof opts.arrayLimit === 'number' ? opts.arrayLimit : defaults.arrayLimit,\n        charset: charset,\n        charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults.charsetSentinel,\n        comma: typeof opts.comma === 'boolean' ? opts.comma : defaults.comma,\n        decodeDotInKeys: typeof opts.decodeDotInKeys === 'boolean' ? opts.decodeDotInKeys : defaults.decodeDotInKeys,\n        decoder: typeof opts.decoder === 'function' ? opts.decoder : defaults.decoder,\n        delimiter: typeof opts.delimiter === 'string' || utils.isRegExp(opts.delimiter) ? opts.delimiter : defaults.delimiter,\n        // eslint-disable-next-line no-implicit-coercion, no-extra-parens\n        depth: (typeof opts.depth === 'number' || opts.depth === false) ? +opts.depth : defaults.depth,\n        duplicates: duplicates,\n        ignoreQueryPrefix: opts.ignoreQueryPrefix === true,\n        interpretNumericEntities: typeof opts.interpretNumericEntities === 'boolean' ? opts.interpretNumericEntities : defaults.interpretNumericEntities,\n        parameterLimit: typeof opts.parameterLimit === 'number' ? opts.parameterLimit : defaults.parameterLimit,\n        parseArrays: opts.parseArrays !== false,\n        plainObjects: typeof opts.plainObjects === 'boolean' ? opts.plainObjects : defaults.plainObjects,\n        strictDepth: typeof opts.strictDepth === 'boolean' ? !!opts.strictDepth : defaults.strictDepth,\n        strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults.strictNullHandling,\n        throwOnLimitExceeded: typeof opts.throwOnLimitExceeded === 'boolean' ? opts.throwOnLimitExceeded : false\n    };\n};\n\nmodule.exports = function (str, opts) {\n    var options = normalizeParseOptions(opts);\n\n    if (str === '' || str === null || typeof str === 'undefined') {\n        return options.plainObjects ? { __proto__: null } : {};\n    }\n\n    var tempObj = typeof str === 'string' ? parseValues(str, options) : str;\n    var obj = options.plainObjects ? { __proto__: null } : {};\n\n    // Iterate over the keys and setup the new object\n\n    var keys = Object.keys(tempObj);\n    for (var i = 0; i < keys.length; ++i) {\n        var key = keys[i];\n        var newObj = parseKeys(key, tempObj[key], options, typeof str === 'string');\n        obj = utils.merge(obj, newObj, options);\n    }\n\n    if (options.allowSparse === true) {\n        return obj;\n    }\n\n    return utils.compact(obj);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,SAAS,CAAC;AAE9B,IAAIC,GAAG,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AACzC,IAAIC,OAAO,GAAGC,KAAK,CAACD,OAAO;AAE3B,IAAIE,QAAQ,GAAG;EACXC,SAAS,EAAE,KAAK;EAChBC,gBAAgB,EAAE,KAAK;EACvBC,eAAe,EAAE,KAAK;EACtBC,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE,EAAE;EACdC,OAAO,EAAE,OAAO;EAChBC,eAAe,EAAE,KAAK;EACtBC,KAAK,EAAE,KAAK;EACZC,eAAe,EAAE,KAAK;EACtBC,OAAO,EAAElB,KAAK,CAACmB,MAAM;EACrBC,SAAS,EAAE,GAAG;EACdC,KAAK,EAAE,CAAC;EACRC,UAAU,EAAE,SAAS;EACrBC,iBAAiB,EAAE,KAAK;EACxBC,wBAAwB,EAAE,KAAK;EAC/BC,cAAc,EAAE,IAAI;EACpBC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,KAAK;EACnBC,WAAW,EAAE,KAAK;EAClBC,kBAAkB,EAAE,KAAK;EACzBC,oBAAoB,EAAE;AAC1B,CAAC;AAED,IAAIN,wBAAwB,GAAG,SAAAA,CAAUO,GAAG,EAAE;EAC1C,OAAOA,GAAG,CAACC,OAAO,CAAC,WAAW,EAAE,UAAUC,EAAE,EAAEC,SAAS,EAAE;IACrD,OAAOC,MAAM,CAACC,YAAY,CAACC,QAAQ,CAACH,SAAS,EAAE,EAAE,CAAC,CAAC;EACvD,CAAC,CAAC;AACN,CAAC;AAED,IAAII,eAAe,GAAG,SAAAA,CAAUC,GAAG,EAAEC,OAAO,EAAEC,kBAAkB,EAAE;EAC9D,IAAIF,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIC,OAAO,CAACxB,KAAK,IAAIuB,GAAG,CAACG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;IAC1E,OAAOH,GAAG,CAACI,KAAK,CAAC,GAAG,CAAC;EACzB;EAEA,IAAIH,OAAO,CAACV,oBAAoB,IAAIW,kBAAkB,IAAID,OAAO,CAAC3B,UAAU,EAAE;IAC1E,MAAM,IAAI+B,UAAU,CAAC,6BAA6B,GAAGJ,OAAO,CAAC3B,UAAU,GAAG,UAAU,IAAI2B,OAAO,CAAC3B,UAAU,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,uBAAuB,CAAC;EAC3J;EAEA,OAAO0B,GAAG;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAIM,WAAW,GAAG,qBAAqB,CAAC,CAAC;;AAEzC;AACA,IAAI9B,eAAe,GAAG,gBAAgB,CAAC,CAAC;;AAExC,IAAI+B,WAAW,GAAG,SAASC,sBAAsBA,CAAChB,GAAG,EAAES,OAAO,EAAE;EAC5D,IAAIQ,GAAG,GAAG;IAAEC,SAAS,EAAE;EAAK,CAAC;EAE7B,IAAIC,QAAQ,GAAGV,OAAO,CAACjB,iBAAiB,GAAGQ,GAAG,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAGD,GAAG;EACvEmB,QAAQ,GAAGA,QAAQ,CAAClB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;EAE/D,IAAImB,KAAK,GAAGX,OAAO,CAACf,cAAc,KAAK2B,QAAQ,GAAGC,SAAS,GAAGb,OAAO,CAACf,cAAc;EACpF,IAAI6B,KAAK,GAAGJ,QAAQ,CAACP,KAAK,CACtBH,OAAO,CAACpB,SAAS,EACjBoB,OAAO,CAACV,oBAAoB,GAAGqB,KAAK,GAAG,CAAC,GAAGA,KAC/C,CAAC;EAED,IAAIX,OAAO,CAACV,oBAAoB,IAAIwB,KAAK,CAACC,MAAM,GAAGJ,KAAK,EAAE;IACtD,MAAM,IAAIP,UAAU,CAAC,iCAAiC,GAAGO,KAAK,GAAG,YAAY,IAAIA,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,WAAW,CAAC;EAC3H;EAEA,IAAIK,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;EACpB,IAAIC,CAAC;EAEL,IAAI3C,OAAO,GAAG0B,OAAO,CAAC1B,OAAO;EAC7B,IAAI0B,OAAO,CAACzB,eAAe,EAAE;IACzB,KAAK0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACC,MAAM,EAAE,EAAEE,CAAC,EAAE;MAC/B,IAAIH,KAAK,CAACG,CAAC,CAAC,CAACf,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACjC,IAAIY,KAAK,CAACG,CAAC,CAAC,KAAK1C,eAAe,EAAE;UAC9BD,OAAO,GAAG,OAAO;QACrB,CAAC,MAAM,IAAIwC,KAAK,CAACG,CAAC,CAAC,KAAKZ,WAAW,EAAE;UACjC/B,OAAO,GAAG,YAAY;QAC1B;QACA0C,SAAS,GAAGC,CAAC;QACbA,CAAC,GAAGH,KAAK,CAACC,MAAM,CAAC,CAAC;MACtB;IACJ;EACJ;EAEA,KAAKE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACC,MAAM,EAAE,EAAEE,CAAC,EAAE;IAC/B,IAAIA,CAAC,KAAKD,SAAS,EAAE;MACjB;IACJ;IACA,IAAIE,IAAI,GAAGJ,KAAK,CAACG,CAAC,CAAC;IAEnB,IAAIE,gBAAgB,GAAGD,IAAI,CAAChB,OAAO,CAAC,IAAI,CAAC;IACzC,IAAIkB,GAAG,GAAGD,gBAAgB,KAAK,CAAC,CAAC,GAAGD,IAAI,CAAChB,OAAO,CAAC,GAAG,CAAC,GAAGiB,gBAAgB,GAAG,CAAC;IAE5E,IAAIE,GAAG;IACP,IAAItB,GAAG;IACP,IAAIqB,GAAG,KAAK,CAAC,CAAC,EAAE;MACZC,GAAG,GAAGrB,OAAO,CAACtB,OAAO,CAACwC,IAAI,EAAElD,QAAQ,CAACU,OAAO,EAAEJ,OAAO,EAAE,KAAK,CAAC;MAC7DyB,GAAG,GAAGC,OAAO,CAACX,kBAAkB,GAAG,IAAI,GAAG,EAAE;IAChD,CAAC,MAAM;MACHgC,GAAG,GAAGrB,OAAO,CAACtB,OAAO,CAACwC,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEF,GAAG,CAAC,EAAEpD,QAAQ,CAACU,OAAO,EAAEJ,OAAO,EAAE,KAAK,CAAC;MAE3EyB,GAAG,GAAGvC,KAAK,CAAC+D,QAAQ,CAChBzB,eAAe,CACXoB,IAAI,CAACI,KAAK,CAACF,GAAG,GAAG,CAAC,CAAC,EACnBpB,OAAO,EACPlC,OAAO,CAAC0C,GAAG,CAACa,GAAG,CAAC,CAAC,GAAGb,GAAG,CAACa,GAAG,CAAC,CAACN,MAAM,GAAG,CAC1C,CAAC,EACD,UAAUS,UAAU,EAAE;QAClB,OAAOxB,OAAO,CAACtB,OAAO,CAAC8C,UAAU,EAAExD,QAAQ,CAACU,OAAO,EAAEJ,OAAO,EAAE,OAAO,CAAC;MAC1E,CACJ,CAAC;IACL;IAEA,IAAIyB,GAAG,IAAIC,OAAO,CAAChB,wBAAwB,IAAIV,OAAO,KAAK,YAAY,EAAE;MACrEyB,GAAG,GAAGf,wBAAwB,CAACW,MAAM,CAACI,GAAG,CAAC,CAAC;IAC/C;IAEA,IAAImB,IAAI,CAAChB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;MAC1BH,GAAG,GAAGjC,OAAO,CAACiC,GAAG,CAAC,GAAG,CAACA,GAAG,CAAC,GAAGA,GAAG;IACpC;IAEA,IAAI0B,QAAQ,GAAG/D,GAAG,CAACgE,IAAI,CAAClB,GAAG,EAAEa,GAAG,CAAC;IACjC,IAAII,QAAQ,IAAIzB,OAAO,CAAClB,UAAU,KAAK,SAAS,EAAE;MAC9C0B,GAAG,CAACa,GAAG,CAAC,GAAG7D,KAAK,CAACmE,OAAO,CAACnB,GAAG,CAACa,GAAG,CAAC,EAAEtB,GAAG,CAAC;IAC3C,CAAC,MAAM,IAAI,CAAC0B,QAAQ,IAAIzB,OAAO,CAAClB,UAAU,KAAK,MAAM,EAAE;MACnD0B,GAAG,CAACa,GAAG,CAAC,GAAGtB,GAAG;IAClB;EACJ;EAEA,OAAOS,GAAG;AACd,CAAC;AAED,IAAIoB,WAAW,GAAG,SAAAA,CAAUC,KAAK,EAAE9B,GAAG,EAAEC,OAAO,EAAE8B,YAAY,EAAE;EAC3D,IAAI7B,kBAAkB,GAAG,CAAC;EAC1B,IAAI4B,KAAK,CAACd,MAAM,GAAG,CAAC,IAAIc,KAAK,CAACA,KAAK,CAACd,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;IACtD,IAAIgB,SAAS,GAAGF,KAAK,CAACP,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACU,IAAI,CAAC,EAAE,CAAC;IAC3C/B,kBAAkB,GAAGlC,KAAK,CAACD,OAAO,CAACiC,GAAG,CAAC,IAAIA,GAAG,CAACgC,SAAS,CAAC,GAAGhC,GAAG,CAACgC,SAAS,CAAC,CAAChB,MAAM,GAAG,CAAC;EACzF;EAEA,IAAIkB,IAAI,GAAGH,YAAY,GAAG/B,GAAG,GAAGD,eAAe,CAACC,GAAG,EAAEC,OAAO,EAAEC,kBAAkB,CAAC;EAEjF,KAAK,IAAIgB,CAAC,GAAGY,KAAK,CAACd,MAAM,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;IACxC,IAAIT,GAAG;IACP,IAAI0B,IAAI,GAAGL,KAAK,CAACZ,CAAC,CAAC;IAEnB,IAAIiB,IAAI,KAAK,IAAI,IAAIlC,OAAO,CAACd,WAAW,EAAE;MACtCsB,GAAG,GAAGR,OAAO,CAAC9B,gBAAgB,KAAK+D,IAAI,KAAK,EAAE,IAAKjC,OAAO,CAACX,kBAAkB,IAAI4C,IAAI,KAAK,IAAK,CAAC,GAC1F,EAAE,GACFzE,KAAK,CAACmE,OAAO,CAAC,EAAE,EAAEM,IAAI,CAAC;IACjC,CAAC,MAAM;MACHzB,GAAG,GAAGR,OAAO,CAACb,YAAY,GAAG;QAAEsB,SAAS,EAAE;MAAK,CAAC,GAAG,CAAC,CAAC;MACrD,IAAI0B,SAAS,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIF,IAAI,CAACE,MAAM,CAACF,IAAI,CAACnB,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,GAAGmB,IAAI,CAACZ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGY,IAAI;MACzG,IAAIG,WAAW,GAAGrC,OAAO,CAACvB,eAAe,GAAG0D,SAAS,CAAC3C,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG2C,SAAS;MACtF,IAAIG,KAAK,GAAGzC,QAAQ,CAACwC,WAAW,EAAE,EAAE,CAAC;MACrC,IAAI,CAACrC,OAAO,CAACd,WAAW,IAAImD,WAAW,KAAK,EAAE,EAAE;QAC5C7B,GAAG,GAAG;UAAE,CAAC,EAAEyB;QAAK,CAAC;MACrB,CAAC,MAAM,IACH,CAACM,KAAK,CAACD,KAAK,CAAC,IACVJ,IAAI,KAAKG,WAAW,IACpB1C,MAAM,CAAC2C,KAAK,CAAC,KAAKD,WAAW,IAC7BC,KAAK,IAAI,CAAC,IACTtC,OAAO,CAACd,WAAW,IAAIoD,KAAK,IAAItC,OAAO,CAAC3B,UAAW,EACzD;QACEmC,GAAG,GAAG,EAAE;QACRA,GAAG,CAAC8B,KAAK,CAAC,GAAGL,IAAI;MACrB,CAAC,MAAM,IAAII,WAAW,KAAK,WAAW,EAAE;QACpC7B,GAAG,CAAC6B,WAAW,CAAC,GAAGJ,IAAI;MAC3B;IACJ;IAEAA,IAAI,GAAGzB,GAAG;EACd;EAEA,OAAOyB,IAAI;AACf,CAAC;AAED,IAAIO,SAAS,GAAG,SAASC,oBAAoBA,CAACC,QAAQ,EAAE3C,GAAG,EAAEC,OAAO,EAAE8B,YAAY,EAAE;EAChF,IAAI,CAACY,QAAQ,EAAE;IACX;EACJ;;EAEA;EACA,IAAIrB,GAAG,GAAGrB,OAAO,CAAC/B,SAAS,GAAGyE,QAAQ,CAAClD,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,GAAGkD,QAAQ;;EAEhF;;EAEA,IAAIC,QAAQ,GAAG,cAAc;EAC7B,IAAIC,KAAK,GAAG,eAAe;;EAE3B;;EAEA,IAAIC,OAAO,GAAG7C,OAAO,CAACnB,KAAK,GAAG,CAAC,IAAI8D,QAAQ,CAACG,IAAI,CAACzB,GAAG,CAAC;EACrD,IAAI0B,MAAM,GAAGF,OAAO,GAAGxB,GAAG,CAACC,KAAK,CAAC,CAAC,EAAEuB,OAAO,CAACP,KAAK,CAAC,GAAGjB,GAAG;;EAExD;;EAEA,IAAI2B,IAAI,GAAG,EAAE;EACb,IAAID,MAAM,EAAE;IACR;IACA,IAAI,CAAC/C,OAAO,CAACb,YAAY,IAAIzB,GAAG,CAACgE,IAAI,CAAC/D,MAAM,CAACC,SAAS,EAAEmF,MAAM,CAAC,EAAE;MAC7D,IAAI,CAAC/C,OAAO,CAAC7B,eAAe,EAAE;QAC1B;MACJ;IACJ;IAEA6E,IAAI,CAACC,IAAI,CAACF,MAAM,CAAC;EACrB;;EAEA;;EAEA,IAAI9B,CAAC,GAAG,CAAC;EACT,OAAOjB,OAAO,CAACnB,KAAK,GAAG,CAAC,IAAI,CAACgE,OAAO,GAAGD,KAAK,CAACE,IAAI,CAACzB,GAAG,CAAC,MAAM,IAAI,IAAIJ,CAAC,GAAGjB,OAAO,CAACnB,KAAK,EAAE;IACnFoC,CAAC,IAAI,CAAC;IACN,IAAI,CAACjB,OAAO,CAACb,YAAY,IAAIzB,GAAG,CAACgE,IAAI,CAAC/D,MAAM,CAACC,SAAS,EAAEiF,OAAO,CAAC,CAAC,CAAC,CAACvB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9E,IAAI,CAACtB,OAAO,CAAC7B,eAAe,EAAE;QAC1B;MACJ;IACJ;IACA6E,IAAI,CAACC,IAAI,CAACJ,OAAO,CAAC,CAAC,CAAC,CAAC;EACzB;;EAEA;;EAEA,IAAIA,OAAO,EAAE;IACT,IAAI7C,OAAO,CAACZ,WAAW,KAAK,IAAI,EAAE;MAC9B,MAAM,IAAIgB,UAAU,CAAC,uCAAuC,GAAGJ,OAAO,CAACnB,KAAK,GAAG,0BAA0B,CAAC;IAC9G;IACAmE,IAAI,CAACC,IAAI,CAAC,GAAG,GAAG5B,GAAG,CAACC,KAAK,CAACuB,OAAO,CAACP,KAAK,CAAC,GAAG,GAAG,CAAC;EACnD;EAEA,OAAOV,WAAW,CAACoB,IAAI,EAAEjD,GAAG,EAAEC,OAAO,EAAE8B,YAAY,CAAC;AACxD,CAAC;AAED,IAAIoB,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,IAAI,EAAE;EAC7D,IAAI,CAACA,IAAI,EAAE;IACP,OAAOnF,QAAQ;EACnB;EAEA,IAAI,OAAOmF,IAAI,CAACjF,gBAAgB,KAAK,WAAW,IAAI,OAAOiF,IAAI,CAACjF,gBAAgB,KAAK,SAAS,EAAE;IAC5F,MAAM,IAAIkF,SAAS,CAAC,wEAAwE,CAAC;EACjG;EAEA,IAAI,OAAOD,IAAI,CAAC1E,eAAe,KAAK,WAAW,IAAI,OAAO0E,IAAI,CAAC1E,eAAe,KAAK,SAAS,EAAE;IAC1F,MAAM,IAAI2E,SAAS,CAAC,uEAAuE,CAAC;EAChG;EAEA,IAAID,IAAI,CAACzE,OAAO,KAAK,IAAI,IAAI,OAAOyE,IAAI,CAACzE,OAAO,KAAK,WAAW,IAAI,OAAOyE,IAAI,CAACzE,OAAO,KAAK,UAAU,EAAE;IACpG,MAAM,IAAI0E,SAAS,CAAC,+BAA+B,CAAC;EACxD;EAEA,IAAI,OAAOD,IAAI,CAAC7E,OAAO,KAAK,WAAW,IAAI6E,IAAI,CAAC7E,OAAO,KAAK,OAAO,IAAI6E,IAAI,CAAC7E,OAAO,KAAK,YAAY,EAAE;IAClG,MAAM,IAAI8E,SAAS,CAAC,mEAAmE,CAAC;EAC5F;EAEA,IAAI,OAAOD,IAAI,CAAC7D,oBAAoB,KAAK,WAAW,IAAI,OAAO6D,IAAI,CAAC7D,oBAAoB,KAAK,SAAS,EAAE;IACpG,MAAM,IAAI8D,SAAS,CAAC,iDAAiD,CAAC;EAC1E;EAEA,IAAI9E,OAAO,GAAG,OAAO6E,IAAI,CAAC7E,OAAO,KAAK,WAAW,GAAGN,QAAQ,CAACM,OAAO,GAAG6E,IAAI,CAAC7E,OAAO;EAEnF,IAAIQ,UAAU,GAAG,OAAOqE,IAAI,CAACrE,UAAU,KAAK,WAAW,GAAGd,QAAQ,CAACc,UAAU,GAAGqE,IAAI,CAACrE,UAAU;EAE/F,IAAIA,UAAU,KAAK,SAAS,IAAIA,UAAU,KAAK,OAAO,IAAIA,UAAU,KAAK,MAAM,EAAE;IAC7E,MAAM,IAAIsE,SAAS,CAAC,8DAA8D,CAAC;EACvF;EAEA,IAAInF,SAAS,GAAG,OAAOkF,IAAI,CAAClF,SAAS,KAAK,WAAW,GAAGkF,IAAI,CAAC1E,eAAe,KAAK,IAAI,GAAG,IAAI,GAAGT,QAAQ,CAACC,SAAS,GAAG,CAAC,CAACkF,IAAI,CAAClF,SAAS;EAEpI,OAAO;IACHA,SAAS,EAAEA,SAAS;IACpBC,gBAAgB,EAAE,OAAOiF,IAAI,CAACjF,gBAAgB,KAAK,SAAS,GAAG,CAAC,CAACiF,IAAI,CAACjF,gBAAgB,GAAGF,QAAQ,CAACE,gBAAgB;IAClHC,eAAe,EAAE,OAAOgF,IAAI,CAAChF,eAAe,KAAK,SAAS,GAAGgF,IAAI,CAAChF,eAAe,GAAGH,QAAQ,CAACG,eAAe;IAC5GC,WAAW,EAAE,OAAO+E,IAAI,CAAC/E,WAAW,KAAK,SAAS,GAAG+E,IAAI,CAAC/E,WAAW,GAAGJ,QAAQ,CAACI,WAAW;IAC5FC,UAAU,EAAE,OAAO8E,IAAI,CAAC9E,UAAU,KAAK,QAAQ,GAAG8E,IAAI,CAAC9E,UAAU,GAAGL,QAAQ,CAACK,UAAU;IACvFC,OAAO,EAAEA,OAAO;IAChBC,eAAe,EAAE,OAAO4E,IAAI,CAAC5E,eAAe,KAAK,SAAS,GAAG4E,IAAI,CAAC5E,eAAe,GAAGP,QAAQ,CAACO,eAAe;IAC5GC,KAAK,EAAE,OAAO2E,IAAI,CAAC3E,KAAK,KAAK,SAAS,GAAG2E,IAAI,CAAC3E,KAAK,GAAGR,QAAQ,CAACQ,KAAK;IACpEC,eAAe,EAAE,OAAO0E,IAAI,CAAC1E,eAAe,KAAK,SAAS,GAAG0E,IAAI,CAAC1E,eAAe,GAAGT,QAAQ,CAACS,eAAe;IAC5GC,OAAO,EAAE,OAAOyE,IAAI,CAACzE,OAAO,KAAK,UAAU,GAAGyE,IAAI,CAACzE,OAAO,GAAGV,QAAQ,CAACU,OAAO;IAC7EE,SAAS,EAAE,OAAOuE,IAAI,CAACvE,SAAS,KAAK,QAAQ,IAAIpB,KAAK,CAAC6F,QAAQ,CAACF,IAAI,CAACvE,SAAS,CAAC,GAAGuE,IAAI,CAACvE,SAAS,GAAGZ,QAAQ,CAACY,SAAS;IACrH;IACAC,KAAK,EAAG,OAAOsE,IAAI,CAACtE,KAAK,KAAK,QAAQ,IAAIsE,IAAI,CAACtE,KAAK,KAAK,KAAK,GAAI,CAACsE,IAAI,CAACtE,KAAK,GAAGb,QAAQ,CAACa,KAAK;IAC9FC,UAAU,EAAEA,UAAU;IACtBC,iBAAiB,EAAEoE,IAAI,CAACpE,iBAAiB,KAAK,IAAI;IAClDC,wBAAwB,EAAE,OAAOmE,IAAI,CAACnE,wBAAwB,KAAK,SAAS,GAAGmE,IAAI,CAACnE,wBAAwB,GAAGhB,QAAQ,CAACgB,wBAAwB;IAChJC,cAAc,EAAE,OAAOkE,IAAI,CAAClE,cAAc,KAAK,QAAQ,GAAGkE,IAAI,CAAClE,cAAc,GAAGjB,QAAQ,CAACiB,cAAc;IACvGC,WAAW,EAAEiE,IAAI,CAACjE,WAAW,KAAK,KAAK;IACvCC,YAAY,EAAE,OAAOgE,IAAI,CAAChE,YAAY,KAAK,SAAS,GAAGgE,IAAI,CAAChE,YAAY,GAAGnB,QAAQ,CAACmB,YAAY;IAChGC,WAAW,EAAE,OAAO+D,IAAI,CAAC/D,WAAW,KAAK,SAAS,GAAG,CAAC,CAAC+D,IAAI,CAAC/D,WAAW,GAAGpB,QAAQ,CAACoB,WAAW;IAC9FC,kBAAkB,EAAE,OAAO8D,IAAI,CAAC9D,kBAAkB,KAAK,SAAS,GAAG8D,IAAI,CAAC9D,kBAAkB,GAAGrB,QAAQ,CAACqB,kBAAkB;IACxHC,oBAAoB,EAAE,OAAO6D,IAAI,CAAC7D,oBAAoB,KAAK,SAAS,GAAG6D,IAAI,CAAC7D,oBAAoB,GAAG;EACvG,CAAC;AACL,CAAC;AAEDgE,MAAM,CAACC,OAAO,GAAG,UAAUhE,GAAG,EAAE4D,IAAI,EAAE;EAClC,IAAInD,OAAO,GAAGkD,qBAAqB,CAACC,IAAI,CAAC;EAEzC,IAAI5D,GAAG,KAAK,EAAE,IAAIA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,WAAW,EAAE;IAC1D,OAAOS,OAAO,CAACb,YAAY,GAAG;MAAEsB,SAAS,EAAE;IAAK,CAAC,GAAG,CAAC,CAAC;EAC1D;EAEA,IAAI+C,OAAO,GAAG,OAAOjE,GAAG,KAAK,QAAQ,GAAGe,WAAW,CAACf,GAAG,EAAES,OAAO,CAAC,GAAGT,GAAG;EACvE,IAAIiB,GAAG,GAAGR,OAAO,CAACb,YAAY,GAAG;IAAEsB,SAAS,EAAE;EAAK,CAAC,GAAG,CAAC,CAAC;;EAEzD;;EAEA,IAAIuC,IAAI,GAAGrF,MAAM,CAACqF,IAAI,CAACQ,OAAO,CAAC;EAC/B,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,IAAI,CAACjC,MAAM,EAAE,EAAEE,CAAC,EAAE;IAClC,IAAII,GAAG,GAAG2B,IAAI,CAAC/B,CAAC,CAAC;IACjB,IAAIwC,MAAM,GAAGjB,SAAS,CAACnB,GAAG,EAAEmC,OAAO,CAACnC,GAAG,CAAC,EAAErB,OAAO,EAAE,OAAOT,GAAG,KAAK,QAAQ,CAAC;IAC3EiB,GAAG,GAAGhD,KAAK,CAACkG,KAAK,CAAClD,GAAG,EAAEiD,MAAM,EAAEzD,OAAO,CAAC;EAC3C;EAEA,IAAIA,OAAO,CAAC5B,WAAW,KAAK,IAAI,EAAE;IAC9B,OAAOoC,GAAG;EACd;EAEA,OAAOhD,KAAK,CAACmG,OAAO,CAACnD,GAAG,CAAC;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}