{"ast": null, "code": "'use strict';\n\nvar IS_PURE = require('../internals/is-pure');\nvar globalThis = require('../internals/global-this');\nvar defineGlobalProperty = require('../internals/define-global-property');\nvar SHARED = '__core-js_shared__';\nvar store = module.exports = globalThis[SHARED] || defineGlobalProperty(SHARED, {});\n(store.versions || (store.versions = [])).push({\n  version: '3.42.0',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2014-2025 <PERSON> (zloirock.ru)',\n  license: 'https://github.com/zloirock/core-js/blob/v3.42.0/LICENSE',\n  source: 'https://github.com/zloirock/core-js'\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}