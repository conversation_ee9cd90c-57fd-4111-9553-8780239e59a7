{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\FactoryExpenses.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { formatCurrency, formatDate } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FactoryExpenses = () => {\n  _s();\n  const [expenses, setExpenses] = useState([{\n    id: 1,\n    date: '2024-01-15',\n    expenseCategory: 'صيانة',\n    description: 'صيانة دورية للآلات',\n    amount: 5500,\n    department: 'الإنتاج',\n    approvedBy: 'مدير المصنع',\n    receiptNumber: 'FAC-001'\n  }, {\n    id: 2,\n    date: '2024-01-14',\n    expenseCategory: 'كهرباء',\n    description: 'فاتورة الكهرباء الشهرية',\n    amount: 8200,\n    department: 'المرافق',\n    approvedBy: 'مدير المصنع',\n    receiptNumber: 'FAC-002'\n  }, {\n    id: 3,\n    date: '2024-01-13',\n    expenseCategory: 'مواد استهلاكية',\n    description: 'زيوت وشحوم للآلات',\n    amount: 3500,\n    department: 'الصيانة',\n    approvedBy: 'رئيس الصيانة',\n    receiptNumber: 'FAC-003'\n  }]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"factory-expenses\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0645\\u0635\\u0646\\u0639\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0635\\u0631\\u0648\\u0641 \\u0645\\u0635\\u0646\\u0639\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0641\\u0626\\u0629 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0642\\u0633\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0645\\u0639\\u062A\\u0645\\u062F \\u0645\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0625\\u064A\\u0635\\u0627\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: expenses.map(expense => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatDate(expense.date)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.expenseCategory\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  fontWeight: 'bold',\n                  color: '#dc3545'\n                },\n                children: formatCurrency(expense.amount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.department\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.approvedBy\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.receiptNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '5px 10px',\n                    fontSize: '12px'\n                  },\n                  children: \"\\u270F\\uFE0F \\u062A\\u0639\\u062F\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 19\n              }, this)]\n            }, expense.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(FactoryExpenses, \"RtvDwao0zso1dbz9Dmb2F6Fgp6c=\");\n_c = FactoryExpenses;\nexport default FactoryExpenses;\nvar _c;\n$RefreshReg$(_c, \"FactoryExpenses\");", "map": {"version": 3, "names": ["React", "useState", "formatCurrency", "formatDate", "jsxDEV", "_jsxDEV", "FactoryExpenses", "_s", "expenses", "setExpenses", "id", "date", "expenseCategory", "description", "amount", "department", "approvedBy", "receiptNumber", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "expense", "style", "fontWeight", "color", "padding", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/FactoryExpenses.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { formatCurrency, formatDate } from '../utils/currency';\n\nconst FactoryExpenses = () => {\n  const [expenses, setExpenses] = useState([\n    {\n      id: 1,\n      date: '2024-01-15',\n      expenseCategory: 'صيانة',\n      description: 'صيانة دورية للآلات',\n      amount: 5500,\n      department: 'الإنتاج',\n      approvedBy: 'مدير المصنع',\n      receiptNumber: 'FAC-001'\n    },\n    {\n      id: 2,\n      date: '2024-01-14',\n      expenseCategory: 'كهرباء',\n      description: 'فاتورة الكهرباء الشهرية',\n      amount: 8200,\n      department: 'المرافق',\n      approvedBy: 'مدير المصنع',\n      receiptNumber: 'FAC-002'\n    },\n    {\n      id: 3,\n      date: '2024-01-13',\n      expenseCategory: 'مواد استهلاكية',\n      description: 'زيوت وشحوم للآلات',\n      amount: 3500,\n      department: 'الصيانة',\n      approvedBy: 'رئيس الصيانة',\n      receiptNumber: 'FAC-003'\n    }\n  ]);\n\n  return (\n    <div className=\"factory-expenses\">\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">مصروفات المصنع</h3>\n          <button className=\"btn btn-primary\">\n            ➕ إضافة مصروف مصنع\n          </button>\n        </div>\n\n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>التاريخ</th>\n                <th>فئة المصروف</th>\n                <th>الوصف</th>\n                <th>المبلغ</th>\n                <th>القسم</th>\n                <th>معتمد من</th>\n                <th>رقم الإيصال</th>\n                <th>إجراءات</th>\n              </tr>\n            </thead>\n            <tbody>\n              {expenses.map((expense) => (\n                <tr key={expense.id}>\n                  <td>{formatDate(expense.date)}</td>\n                  <td>{expense.expenseCategory}</td>\n                  <td>{expense.description}</td>\n                  <td style={{ fontWeight: 'bold', color: '#dc3545' }}>\n                    {formatCurrency(expense.amount)}\n                  </td>\n                  <td>{expense.department}</td>\n                  <td>{expense.approvedBy}</td>\n                  <td>{expense.receiptNumber}</td>\n                  <td>\n                    <button className=\"btn btn-secondary\" style={{ padding: '5px 10px', fontSize: '12px' }}>\n                      ✏️ تعديل\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FactoryExpenses;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,cAAc,EAAEC,UAAU,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,CACvC;IACES,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,eAAe,EAAE,OAAO;IACxBC,WAAW,EAAE,oBAAoB;IACjCC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE,aAAa;IACzBC,aAAa,EAAE;EACjB,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,eAAe,EAAE,QAAQ;IACzBC,WAAW,EAAE,yBAAyB;IACtCC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE,aAAa;IACzBC,aAAa,EAAE;EACjB,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,eAAe,EAAE,gBAAgB;IACjCC,WAAW,EAAE,mBAAmB;IAChCC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE,cAAc;IAC1BC,aAAa,EAAE;EACjB,CAAC,CACF,CAAC;EAEF,oBACEZ,OAAA;IAAKa,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAC/Bd,OAAA;MAAKa,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBd,OAAA;QAAKa,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1Bd,OAAA;UAAIa,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9ClB,OAAA;UAAQa,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAEpC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlB,OAAA;QAAKa,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9Bd,OAAA;UAAOa,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtBd,OAAA;YAAAc,QAAA,eACEd,OAAA;cAAAc,QAAA,gBACEd,OAAA;gBAAAc,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBlB,OAAA;gBAAAc,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBlB,OAAA;gBAAAc,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdlB,OAAA;gBAAAc,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACflB,OAAA;gBAAAc,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdlB,OAAA;gBAAAc,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBlB,OAAA;gBAAAc,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBlB,OAAA;gBAAAc,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRlB,OAAA;YAAAc,QAAA,EACGX,QAAQ,CAACgB,GAAG,CAAEC,OAAO,iBACpBpB,OAAA;cAAAc,QAAA,gBACEd,OAAA;gBAAAc,QAAA,EAAKhB,UAAU,CAACsB,OAAO,CAACd,IAAI;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnClB,OAAA;gBAAAc,QAAA,EAAKM,OAAO,CAACb;cAAe;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClClB,OAAA;gBAAAc,QAAA,EAAKM,OAAO,CAACZ;cAAW;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9BlB,OAAA;gBAAIqB,KAAK,EAAE;kBAAEC,UAAU,EAAE,MAAM;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAT,QAAA,EACjDjB,cAAc,CAACuB,OAAO,CAACX,MAAM;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACLlB,OAAA;gBAAAc,QAAA,EAAKM,OAAO,CAACV;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7BlB,OAAA;gBAAAc,QAAA,EAAKM,OAAO,CAACT;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7BlB,OAAA;gBAAAc,QAAA,EAAKM,OAAO,CAACR;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChClB,OAAA;gBAAAc,QAAA,eACEd,OAAA;kBAAQa,SAAS,EAAC,mBAAmB;kBAACQ,KAAK,EAAE;oBAAEG,OAAO,EAAE,UAAU;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAAX,QAAA,EAAC;gBAExF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAdEE,OAAO,CAACf,EAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAef,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CAnFID,eAAe;AAAAyB,EAAA,GAAfzB,eAAe;AAqFrB,eAAeA,eAAe;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}