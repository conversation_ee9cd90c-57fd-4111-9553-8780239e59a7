{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\LivingExpenses.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { formatCurrency } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LivingExpenses = () => {\n  _s();\n  const [expenses, setExpenses] = useState([{\n    id: 1,\n    date: '2024-01-15',\n    expenseType: 'إقامة فندقية',\n    amount: 2500,\n    beneficiary: 'أحمد محمد - موظف المبيعات',\n    description: 'إقامة فندقية لمدة 3 أيام - مهمة عمل',\n    receiptNumber: 'REC-001',\n    location: 'فندق الكورنيش - طرابلس',\n    duration: '3 أيام'\n  }, {\n    id: 2,\n    date: '2024-01-14',\n    expenseType: 'وجبات طعام',\n    amount: 800,\n    beneficiary: 'فريق الصيانة',\n    description: 'وجبات طعام أثناء العمل الميداني',\n    receiptNumber: 'REC-002',\n    location: 'مطعم النخيل',\n    duration: 'يوم واحد'\n  }, {\n    id: 3,\n    date: '2024-01-13',\n    expenseType: 'مواصلات',\n    amount: 450,\n    beneficiary: 'سالم عبدالله - مهندس',\n    description: 'مواصلات للموقع والعودة',\n    receiptNumber: 'REC-003',\n    location: 'طرابلس - مصراتة',\n    duration: 'يوم واحد'\n  }, {\n    id: 4,\n    date: '2024-01-12',\n    expenseType: 'إقامة مؤقتة',\n    amount: 1200,\n    beneficiary: 'فريق التركيب',\n    description: 'إقامة مؤقتة لفريق التركيب',\n    receiptNumber: 'REC-004',\n    location: 'شقق مفروشة - بنغازي',\n    duration: '2 أيام'\n  }, {\n    id: 5,\n    date: '2024-01-11',\n    expenseType: 'بدل سفر',\n    amount: 600,\n    beneficiary: 'محمد أحمد - مدير المشروع',\n    description: 'بدل سفر ومصروفات شخصية',\n    receiptNumber: 'REC-005',\n    location: 'سبها',\n    duration: 'يومين'\n  }]);\n  const getTotalExpenses = () => {\n    return expenses.reduce((sum, expense) => sum + expense.amount, 0);\n  };\n  const getExpensesByType = () => {\n    const types = {};\n    expenses.forEach(expense => {\n      types[expense.expenseType] = (types[expense.expenseType] || 0) + expense.amount;\n    });\n    return types;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"living-expenses\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#dc3545'\n          },\n          children: formatCurrency(getTotalExpenses())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0645\\u0639\\u064A\\u0634\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#1e3a8a'\n          },\n          children: expenses.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#ffa500'\n          },\n          children: Object.keys(getExpensesByType()).length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0623\\u0646\\u0648\\u0627\\u0639 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#ea580c'\n          },\n          children: formatCurrency(getTotalExpenses() / expenses.length)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0645\\u062A\\u0648\\u0633\\u0637 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0645\\u0639\\u064A\\u0634\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0635\\u0631\\u0648\\u0641 \\u0645\\u0639\\u064A\\u0634\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0645\\u0633\\u062A\\u0641\\u064A\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0645\\u0643\\u0627\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0645\\u062F\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0625\\u064A\\u0635\\u0627\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: expenses.map(expense => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(expense.date).toLocaleDateString('ar-SA')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.expenseType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  fontWeight: 'bold',\n                  color: '#dc3545'\n                },\n                children: formatCurrency(expense.amount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.beneficiary\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.duration\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.receiptNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '5px 10px',\n                    fontSize: '12px'\n                  },\n                  children: \"\\u270F\\uFE0F \\u062A\\u0639\\u062F\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this)]\n            }, expense.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(LivingExpenses, \"xZWWEsL7qp9tQLmADdGGFEqjCnU=\");\n_c = LivingExpenses;\nexport default LivingExpenses;\nvar _c;\n$RefreshReg$(_c, \"LivingExpenses\");", "map": {"version": 3, "names": ["React", "useState", "formatCurrency", "jsxDEV", "_jsxDEV", "LivingExpenses", "_s", "expenses", "setExpenses", "id", "date", "expenseType", "amount", "beneficiary", "description", "receiptNumber", "location", "duration", "getTotalExpenses", "reduce", "sum", "expense", "getExpensesByType", "types", "for<PERSON>ach", "className", "children", "style", "marginBottom", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "Object", "keys", "map", "Date", "toLocaleDateString", "fontWeight", "padding", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/LivingExpenses.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { formatCurrency } from '../utils/currency';\n\nconst LivingExpenses = () => {\n  const [expenses, setExpenses] = useState([\n    {\n      id: 1,\n      date: '2024-01-15',\n      expenseType: 'إقامة فندقية',\n      amount: 2500,\n      beneficiary: 'أحمد محمد - موظف المبيعات',\n      description: 'إقامة فندقية لمدة 3 أيام - مهمة عمل',\n      receiptNumber: 'REC-001',\n      location: 'فندق الكورنيش - طرابلس',\n      duration: '3 أيام'\n    },\n    {\n      id: 2,\n      date: '2024-01-14',\n      expenseType: 'وجبات طعام',\n      amount: 800,\n      beneficiary: 'فريق الصيانة',\n      description: 'وجبات طعام أثناء العمل الميداني',\n      receiptNumber: 'REC-002',\n      location: 'مطعم النخيل',\n      duration: 'يوم واحد'\n    },\n    {\n      id: 3,\n      date: '2024-01-13',\n      expenseType: 'مواصلات',\n      amount: 450,\n      beneficiary: 'سالم عبدالله - مهندس',\n      description: 'مواصلات للموقع والعودة',\n      receiptNumber: 'REC-003',\n      location: 'طرابلس - مصراتة',\n      duration: 'يوم واحد'\n    },\n    {\n      id: 4,\n      date: '2024-01-12',\n      expenseType: 'إقامة مؤقتة',\n      amount: 1200,\n      beneficiary: 'فريق التركيب',\n      description: 'إقامة مؤقتة لفريق التركيب',\n      receiptNumber: 'REC-004',\n      location: 'شقق مفروشة - بنغازي',\n      duration: '2 أيام'\n    },\n    {\n      id: 5,\n      date: '2024-01-11',\n      expenseType: 'بدل سفر',\n      amount: 600,\n      beneficiary: 'محمد أحمد - مدير المشروع',\n      description: 'بدل سفر ومصروفات شخصية',\n      receiptNumber: 'REC-005',\n      location: 'سبها',\n      duration: 'يومين'\n    }\n  ]);\n\n\n\n  const getTotalExpenses = () => {\n    return expenses.reduce((sum, expense) => sum + expense.amount, 0);\n  };\n\n  const getExpensesByType = () => {\n    const types = {};\n    expenses.forEach(expense => {\n      types[expense.expenseType] = (types[expense.expenseType] || 0) + expense.amount;\n    });\n    return types;\n  };\n\n  return (\n    <div className=\"living-expenses\">\n      {/* إحصائيات مصروفات المعيشة */}\n      <div className=\"stats-grid\" style={{ marginBottom: '30px' }}>\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#dc3545' }}>\n            {formatCurrency(getTotalExpenses())}\n          </div>\n          <div className=\"stat-label\">إجمالي مصروفات المعيشة</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#1e3a8a' }}>\n            {expenses.length}\n          </div>\n          <div className=\"stat-label\">عدد المصروفات</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#ffa500' }}>\n            {Object.keys(getExpensesByType()).length}\n          </div>\n          <div className=\"stat-label\">أنواع المصروفات</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#ea580c' }}>\n            {formatCurrency(getTotalExpenses() / expenses.length)}\n          </div>\n          <div className=\"stat-label\">متوسط المصروف</div>\n        </div>\n      </div>\n\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">مصروفات المعيشة</h3>\n          <button className=\"btn btn-primary\">\n            ➕ إضافة مصروف معيشة\n          </button>\n        </div>\n\n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>التاريخ</th>\n                <th>نوع المصروف</th>\n                <th>المبلغ</th>\n                <th>المستفيد</th>\n                <th>المكان</th>\n                <th>المدة</th>\n                <th>الوصف</th>\n                <th>رقم الإيصال</th>\n                <th>إجراءات</th>\n              </tr>\n            </thead>\n            <tbody>\n              {expenses.map((expense) => (\n                <tr key={expense.id}>\n                  <td>{new Date(expense.date).toLocaleDateString('ar-SA')}</td>\n                  <td>{expense.expenseType}</td>\n                  <td style={{ fontWeight: 'bold', color: '#dc3545' }}>\n                    {formatCurrency(expense.amount)}\n                  </td>\n                  <td>{expense.beneficiary}</td>\n                  <td>{expense.location}</td>\n                  <td>{expense.duration}</td>\n                  <td>{expense.description}</td>\n                  <td>{expense.receiptNumber}</td>\n                  <td>\n                    <button className=\"btn btn-secondary\" style={{ padding: '5px 10px', fontSize: '12px' }}>\n                      ✏️ تعديل\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LivingExpenses;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,cAAc,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC,CACvC;IACEQ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,cAAc;IAC3BC,MAAM,EAAE,IAAI;IACZC,WAAW,EAAE,2BAA2B;IACxCC,WAAW,EAAE,qCAAqC;IAClDC,aAAa,EAAE,SAAS;IACxBC,QAAQ,EAAE,wBAAwB;IAClCC,QAAQ,EAAE;EACZ,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,YAAY;IACzBC,MAAM,EAAE,GAAG;IACXC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,iCAAiC;IAC9CC,aAAa,EAAE,SAAS;IACxBC,QAAQ,EAAE,aAAa;IACvBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,GAAG;IACXC,WAAW,EAAE,sBAAsB;IACnCC,WAAW,EAAE,wBAAwB;IACrCC,aAAa,EAAE,SAAS;IACxBC,QAAQ,EAAE,iBAAiB;IAC3BC,QAAQ,EAAE;EACZ,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,aAAa;IAC1BC,MAAM,EAAE,IAAI;IACZC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,2BAA2B;IACxCC,aAAa,EAAE,SAAS;IACxBC,QAAQ,EAAE,qBAAqB;IAC/BC,QAAQ,EAAE;EACZ,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,GAAG;IACXC,WAAW,EAAE,0BAA0B;IACvCC,WAAW,EAAE,wBAAwB;IACrCC,aAAa,EAAE,SAAS;IACxBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE;EACZ,CAAC,CACF,CAAC;EAIF,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAOX,QAAQ,CAACY,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,CAACT,MAAM,EAAE,CAAC,CAAC;EACnE,CAAC;EAED,MAAMU,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,KAAK,GAAG,CAAC,CAAC;IAChBhB,QAAQ,CAACiB,OAAO,CAACH,OAAO,IAAI;MAC1BE,KAAK,CAACF,OAAO,CAACV,WAAW,CAAC,GAAG,CAACY,KAAK,CAACF,OAAO,CAACV,WAAW,CAAC,IAAI,CAAC,IAAIU,OAAO,CAACT,MAAM;IACjF,CAAC,CAAC;IACF,OAAOW,KAAK;EACd,CAAC;EAED,oBACEnB,OAAA;IAAKqB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAE9BtB,OAAA;MAAKqB,SAAS,EAAC,YAAY;MAACE,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBAC1DtB,OAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtB,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrDxB,cAAc,CAACgB,gBAAgB,CAAC,CAAC;QAAC;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACN7B,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAsB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eAEN7B,OAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtB,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrDnB,QAAQ,CAAC2B;QAAM;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACN7B,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eAEN7B,OAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtB,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrDS,MAAM,CAACC,IAAI,CAACd,iBAAiB,CAAC,CAAC,CAAC,CAACY;QAAM;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACN7B,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eAEN7B,OAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtB,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrDxB,cAAc,CAACgB,gBAAgB,CAAC,CAAC,GAAGX,QAAQ,CAAC2B,MAAM;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACN7B,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7B,OAAA;MAAKqB,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBtB,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtB,OAAA;UAAIqB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/C7B,OAAA;UAAQqB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAEpC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN7B,OAAA;QAAKqB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BtB,OAAA;UAAOqB,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtBtB,OAAA;YAAAsB,QAAA,eACEtB,OAAA;cAAAsB,QAAA,gBACEtB,OAAA;gBAAAsB,QAAA,EAAI;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB7B,OAAA;gBAAAsB,QAAA,EAAI;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB7B,OAAA;gBAAAsB,QAAA,EAAI;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf7B,OAAA;gBAAAsB,QAAA,EAAI;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB7B,OAAA;gBAAAsB,QAAA,EAAI;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf7B,OAAA;gBAAAsB,QAAA,EAAI;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACd7B,OAAA;gBAAAsB,QAAA,EAAI;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACd7B,OAAA;gBAAAsB,QAAA,EAAI;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB7B,OAAA;gBAAAsB,QAAA,EAAI;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR7B,OAAA;YAAAsB,QAAA,EACGnB,QAAQ,CAAC8B,GAAG,CAAEhB,OAAO,iBACpBjB,OAAA;cAAAsB,QAAA,gBACEtB,OAAA;gBAAAsB,QAAA,EAAK,IAAIY,IAAI,CAACjB,OAAO,CAACX,IAAI,CAAC,CAAC6B,kBAAkB,CAAC,OAAO;cAAC;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7D7B,OAAA;gBAAAsB,QAAA,EAAKL,OAAO,CAACV;cAAW;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9B7B,OAAA;gBAAIuB,KAAK,EAAE;kBAAEa,UAAU,EAAE,MAAM;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAH,QAAA,EACjDxB,cAAc,CAACmB,OAAO,CAACT,MAAM;cAAC;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACL7B,OAAA;gBAAAsB,QAAA,EAAKL,OAAO,CAACR;cAAW;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9B7B,OAAA;gBAAAsB,QAAA,EAAKL,OAAO,CAACL;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3B7B,OAAA;gBAAAsB,QAAA,EAAKL,OAAO,CAACJ;cAAQ;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3B7B,OAAA;gBAAAsB,QAAA,EAAKL,OAAO,CAACP;cAAW;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9B7B,OAAA;gBAAAsB,QAAA,EAAKL,OAAO,CAACN;cAAa;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChC7B,OAAA;gBAAAsB,QAAA,eACEtB,OAAA;kBAAQqB,SAAS,EAAC,mBAAmB;kBAACE,KAAK,EAAE;oBAAEc,OAAO,EAAE,UAAU;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAAhB,QAAA,EAAC;gBAExF;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAfEZ,OAAO,CAACZ,EAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBf,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA3JID,cAAc;AAAAsC,EAAA,GAAdtC,cAAc;AA6JpB,eAAeA,cAAc;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}