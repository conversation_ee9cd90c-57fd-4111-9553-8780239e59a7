{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\Header.js\";\nimport React from 'react';\nimport { useLocation } from 'react-router-dom';\nconst Header = () => {\n  const location = useLocation();\n  const getPageTitle = () => {\n    const titles = {\n      '/': 'لوحة التحكم',\n      '/account-statements': 'كشف الحساب',\n      '/warehouse-purchases': 'مشتريات المخزن',\n      '/transport-expenses': 'مصروفات النقل',\n      '/living-expenses': 'مصروفات المعيشة',\n      '/paint-expenses': 'مصروفات الطلاء',\n      '/factory-expenses': 'مصروفات المصنع',\n      '/employee-withdrawals': 'مسحوبات الموظفين',\n      '/treasury-deposits': 'إيداعات الخزينة',\n      '/reports': 'التقارير'\n    };\n    return titles[location.pathname] || 'نظام المحاسبة';\n  };\n  const getCurrentDate = () => {\n    const now = new Date();\n    const options = {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      weekday: 'long'\n    };\n    return now.toLocaleDateString('ar-SA', options);\n  };\n  return /*#__PURE__*/React.createElement(\"header\", {\n    className: \"header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 9\n    }\n  }, getPageTitle()), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      fontSize: '14px',\n      color: '#666',\n      marginTop: '5px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 9\n    }\n  }, getCurrentDate())), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"header-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-secondary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 9\n    }\n  }, \"\\u2699\\uFE0F \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 9\n    }\n  }, \"\\uD83D\\uDCBE \\u062D\\u0641\\u0638\")));\n};\nexport default Header;", "map": {"version": 3, "names": ["React", "useLocation", "Header", "location", "getPageTitle", "titles", "pathname", "getCurrentDate", "now", "Date", "options", "year", "month", "day", "weekday", "toLocaleDateString", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "color", "marginTop"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/Header.js"], "sourcesContent": ["import React from 'react';\nimport { useLocation } from 'react-router-dom';\n\nconst Header = () => {\n  const location = useLocation();\n\n  const getPageTitle = () => {\n    const titles = {\n      '/': 'لوحة التحكم',\n      '/account-statements': 'كشف الحساب',\n      '/warehouse-purchases': 'مشتريات المخزن',\n      '/transport-expenses': 'مصروفات النقل',\n      '/living-expenses': 'مصروفات المعيشة',\n      '/paint-expenses': 'مصروفات الطلاء',\n      '/factory-expenses': 'مصروفات المصنع',\n      '/employee-withdrawals': 'مسحوبات الموظفين',\n      '/treasury-deposits': 'إيداعات الخزينة',\n      '/reports': 'التقارير'\n    };\n    return titles[location.pathname] || 'نظام المحاسبة';\n  };\n\n  const getCurrentDate = () => {\n    const now = new Date();\n    const options = {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      weekday: 'long'\n    };\n    return now.toLocaleDateString('ar-SA', options);\n  };\n\n  return (\n    <header className=\"header\">\n      <div>\n        <h1>{getPageTitle()}</h1>\n        <p style={{ fontSize: '14px', color: '#666', marginTop: '5px' }}>\n          {getCurrentDate()}\n        </p>\n      </div>\n      \n      <div className=\"header-actions\">\n        <button className=\"btn btn-secondary\">\n          ⚙️ الإعدادات\n        </button>\n        <button className=\"btn btn-primary\">\n          💾 حفظ\n        </button>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,QAAQ,GAAGF,WAAW,CAAC,CAAC;EAE9B,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG;MACb,GAAG,EAAE,aAAa;MAClB,qBAAqB,EAAE,YAAY;MACnC,sBAAsB,EAAE,gBAAgB;MACxC,qBAAqB,EAAE,eAAe;MACtC,kBAAkB,EAAE,iBAAiB;MACrC,iBAAiB,EAAE,gBAAgB;MACnC,mBAAmB,EAAE,gBAAgB;MACrC,uBAAuB,EAAE,kBAAkB;MAC3C,oBAAoB,EAAE,iBAAiB;MACvC,UAAU,EAAE;IACd,CAAC;IACD,OAAOA,MAAM,CAACF,QAAQ,CAACG,QAAQ,CAAC,IAAI,eAAe;EACrD,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,OAAO,GAAG;MACdC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,OAAO,EAAE;IACX,CAAC;IACD,OAAON,GAAG,CAACO,kBAAkB,CAAC,OAAO,EAAEL,OAAO,CAAC;EACjD,CAAC;EAED,oBACEV,KAAA,CAAAgB,aAAA;IAAQC,SAAS,EAAC,QAAQ;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBvB,KAAA,CAAAgB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEvB,KAAA,CAAAgB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKnB,YAAY,CAAC,CAAM,CAAC,eACzBJ,KAAA,CAAAgB,aAAA;IAAGQ,KAAK,EAAE;MAAEC,QAAQ,EAAE,MAAM;MAAEC,KAAK,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAM,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7DhB,cAAc,CAAC,CACf,CACA,CAAC,eAENP,KAAA,CAAAgB,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BvB,KAAA,CAAAgB,aAAA;IAAQC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,qEAE9B,CAAC,eACTvB,KAAA,CAAAgB,aAAA;IAAQC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,iCAE5B,CACL,CACC,CAAC;AAEb,CAAC;AAED,eAAerB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}