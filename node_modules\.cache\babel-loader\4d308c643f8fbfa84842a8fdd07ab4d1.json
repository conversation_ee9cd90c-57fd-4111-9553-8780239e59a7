{"ast": null, "code": "'use strict';\n\nvar matchOperatorsRe = /[|\\\\{}()[\\]^$+*?.]/g;\nmodule.exports = function (str) {\n  if (typeof str !== 'string') {\n    throw new TypeError('Expected a string');\n  }\n  return str.replace(matchOperatorsRe, '\\\\$&');\n};", "map": {"version": 3, "names": ["matchOperatorsRe", "module", "exports", "str", "TypeError", "replace"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/node_modules/react-dev-utils/node_modules/chalk/node_modules/escape-string-regexp/index.js"], "sourcesContent": ["'use strict';\n\nvar matchOperatorsRe = /[|\\\\{}()[\\]^$+*?.]/g;\n\nmodule.exports = function (str) {\n\tif (typeof str !== 'string') {\n\t\tthrow new TypeError('Expected a string');\n\t}\n\n\treturn str.replace(matchOperatorsRe, '\\\\$&');\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,gBAAgB,GAAG,qBAAqB;AAE5CC,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAE;EAC/B,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAIC,SAAS,CAAC,mBAAmB,CAAC;EACzC;EAEA,OAAOD,GAAG,CAACE,OAAO,CAACL,gBAAgB,EAAE,MAAM,CAAC;AAC7C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}