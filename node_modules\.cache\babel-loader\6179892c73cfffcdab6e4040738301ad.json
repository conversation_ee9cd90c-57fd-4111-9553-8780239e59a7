{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Sidebar from './components/Sidebar';\nimport Header from './components/Header';\nimport Dashboard from './components/Dashboard';\nimport AccountStatements from './components/AccountStatements';\nimport WarehousePurchases from './components/WarehousePurchases';\nimport TransportExpenses from './components/TransportExpenses';\nimport LivingExpenses from './components/LivingExpenses';\nimport PaintExpenses from './components/PaintExpenses';\nimport FactoryExpenses from './components/FactoryExpenses';\nimport EmployeeWithdrawals from './components/EmployeeWithdrawals';\nimport TreasuryDeposits from './components/TreasuryDeposits';\nimport Reports from './components/Reports';\nimport './App.css';\nfunction App() {\n  return /*#__PURE__*/React.createElement(Router, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"app\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Sidebar, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 9\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"main-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Header, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(Routes, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(Route, {\n    path: \"/\",\n    element: /*#__PURE__*/React.createElement(Dashboard, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 40\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 15\n    }\n  }), /*#__PURE__*/React.createElement(Route, {\n    path: \"/account-statements\",\n    element: /*#__PURE__*/React.createElement(AccountStatements, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 58\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 15\n    }\n  }), /*#__PURE__*/React.createElement(Route, {\n    path: \"/warehouse-purchases\",\n    element: /*#__PURE__*/React.createElement(WarehousePurchases, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 59\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 15\n    }\n  }), /*#__PURE__*/React.createElement(Route, {\n    path: \"/transport-expenses\",\n    element: /*#__PURE__*/React.createElement(TransportExpenses, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 58\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 15\n    }\n  }), /*#__PURE__*/React.createElement(Route, {\n    path: \"/living-expenses\",\n    element: /*#__PURE__*/React.createElement(LivingExpenses, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 55\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 15\n    }\n  }), /*#__PURE__*/React.createElement(Route, {\n    path: \"/paint-expenses\",\n    element: /*#__PURE__*/React.createElement(PaintExpenses, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 54\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 15\n    }\n  }), /*#__PURE__*/React.createElement(Route, {\n    path: \"/factory-expenses\",\n    element: /*#__PURE__*/React.createElement(FactoryExpenses, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 56\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 15\n    }\n  }), /*#__PURE__*/React.createElement(Route, {\n    path: \"/employee-withdrawals\",\n    element: /*#__PURE__*/React.createElement(EmployeeWithdrawals, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 60\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 15\n    }\n  }), /*#__PURE__*/React.createElement(Route, {\n    path: \"/treasury-deposits\",\n    element: /*#__PURE__*/React.createElement(TreasuryDeposits, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 57\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 15\n    }\n  }), /*#__PURE__*/React.createElement(Route, {\n    path: \"/reports\",\n    element: /*#__PURE__*/React.createElement(Reports, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 47\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 15\n    }\n  }))))));\n}\nexport default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Sidebar", "Header", "Dashboard", "AccountStatements", "WarehousePurchases", "TransportExpenses", "LivingExpenses", "PaintExpenses", "FactoryExpenses", "EmployeeWithdrawals", "TreasuryDeposits", "Reports", "App", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "path", "element"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Sidebar from './components/Sidebar';\nimport Header from './components/Header';\nimport Dashboard from './components/Dashboard';\nimport AccountStatements from './components/AccountStatements';\nimport WarehousePurchases from './components/WarehousePurchases';\nimport TransportExpenses from './components/TransportExpenses';\nimport LivingExpenses from './components/LivingExpenses';\nimport PaintExpenses from './components/PaintExpenses';\nimport FactoryExpenses from './components/FactoryExpenses';\nimport EmployeeWithdrawals from './components/EmployeeWithdrawals';\nimport TreasuryDeposits from './components/TreasuryDeposits';\nimport Reports from './components/Reports';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"app\">\n        <Sidebar />\n        <div className=\"main-content\">\n          <Header />\n          <div className=\"content\">\n            <Routes>\n              <Route path=\"/\" element={<Dashboard />} />\n              <Route path=\"/account-statements\" element={<AccountStatements />} />\n              <Route path=\"/warehouse-purchases\" element={<WarehousePurchases />} />\n              <Route path=\"/transport-expenses\" element={<TransportExpenses />} />\n              <Route path=\"/living-expenses\" element={<LivingExpenses />} />\n              <Route path=\"/paint-expenses\" element={<PaintExpenses />} />\n              <Route path=\"/factory-expenses\" element={<FactoryExpenses />} />\n              <Route path=\"/employee-withdrawals\" element={<EmployeeWithdrawals />} />\n              <Route path=\"/treasury-deposits\" element={<TreasuryDeposits />} />\n              <Route path=\"/reports\" element={<Reports />} />\n            </Routes>\n          </div>\n        </div>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAO,WAAW;AAElB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACEjB,KAAA,CAAAkB,aAAA,CAAChB,MAAM;IAAAiB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACLxB,KAAA,CAAAkB,aAAA;IAAKO,SAAS,EAAC,KAAK;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClBxB,KAAA,CAAAkB,aAAA,CAACb,OAAO;IAAAc,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACXxB,KAAA,CAAAkB,aAAA;IAAKO,SAAS,EAAC,cAAc;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BxB,KAAA,CAAAkB,aAAA,CAACZ,MAAM;IAAAa,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACVxB,KAAA,CAAAkB,aAAA;IAAKO,SAAS,EAAC,SAAS;IAAAN,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBxB,KAAA,CAAAkB,aAAA,CAACf,MAAM;IAAAgB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACLxB,KAAA,CAAAkB,aAAA,CAACd,KAAK;IAACsB,IAAI,EAAC,GAAG;IAACC,OAAO,eAAE3B,KAAA,CAAAkB,aAAA,CAACX,SAAS;MAAAY,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC1CxB,KAAA,CAAAkB,aAAA,CAACd,KAAK;IAACsB,IAAI,EAAC,qBAAqB;IAACC,OAAO,eAAE3B,KAAA,CAAAkB,aAAA,CAACV,iBAAiB;MAAAW,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACpExB,KAAA,CAAAkB,aAAA,CAACd,KAAK;IAACsB,IAAI,EAAC,sBAAsB;IAACC,OAAO,eAAE3B,KAAA,CAAAkB,aAAA,CAACT,kBAAkB;MAAAU,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACtExB,KAAA,CAAAkB,aAAA,CAACd,KAAK;IAACsB,IAAI,EAAC,qBAAqB;IAACC,OAAO,eAAE3B,KAAA,CAAAkB,aAAA,CAACR,iBAAiB;MAAAS,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACpExB,KAAA,CAAAkB,aAAA,CAACd,KAAK;IAACsB,IAAI,EAAC,kBAAkB;IAACC,OAAO,eAAE3B,KAAA,CAAAkB,aAAA,CAACP,cAAc;MAAAQ,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC9DxB,KAAA,CAAAkB,aAAA,CAACd,KAAK;IAACsB,IAAI,EAAC,iBAAiB;IAACC,OAAO,eAAE3B,KAAA,CAAAkB,aAAA,CAACN,aAAa;MAAAO,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC5DxB,KAAA,CAAAkB,aAAA,CAACd,KAAK;IAACsB,IAAI,EAAC,mBAAmB;IAACC,OAAO,eAAE3B,KAAA,CAAAkB,aAAA,CAACL,eAAe;MAAAM,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAChExB,KAAA,CAAAkB,aAAA,CAACd,KAAK;IAACsB,IAAI,EAAC,uBAAuB;IAACC,OAAO,eAAE3B,KAAA,CAAAkB,aAAA,CAACJ,mBAAmB;MAAAK,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACxExB,KAAA,CAAAkB,aAAA,CAACd,KAAK;IAACsB,IAAI,EAAC,oBAAoB;IAACC,OAAO,eAAE3B,KAAA,CAAAkB,aAAA,CAACH,gBAAgB;MAAAI,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAClExB,KAAA,CAAAkB,aAAA,CAACd,KAAK;IAACsB,IAAI,EAAC,UAAU;IAACC,OAAO,eAAE3B,KAAA,CAAAkB,aAAA,CAACF,OAAO;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACxC,CACL,CACF,CACF,CACC,CAAC;AAEb;AAEA,eAAeP,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}