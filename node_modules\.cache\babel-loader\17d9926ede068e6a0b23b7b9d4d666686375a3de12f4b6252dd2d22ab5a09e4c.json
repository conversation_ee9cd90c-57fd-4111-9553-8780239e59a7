{"ast": null, "code": "'use strict';\n\nvar isCallable = require('../internals/is-callable');\nvar tryToString = require('../internals/try-to-string');\nvar $TypeError = TypeError;\n\n// `Assert: IsCallable(argument) is true`\nmodule.exports = function (argument) {\n  if (isCallable(argument)) return argument;\n  throw new $TypeError(tryToString(argument) + ' is not a function');\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}