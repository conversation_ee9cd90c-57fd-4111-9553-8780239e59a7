{"ast": null, "code": "'use strict';\n\nvar bind = require('function-bind');\nvar $apply = require('./functionApply');\nvar $call = require('./functionCall');\nvar $reflectApply = require('./reflectApply');\n\n/** @type {import('./actualApply')} */\nmodule.exports = $reflectApply || bind.call($call, $apply);", "map": {"version": 3, "names": ["bind", "require", "$apply", "$call", "$reflectApply", "module", "exports", "call"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/node_modules/call-bind-apply-helpers/actualApply.js"], "sourcesContent": ["'use strict';\n\nvar bind = require('function-bind');\n\nvar $apply = require('./functionApply');\nvar $call = require('./functionCall');\nvar $reflectApply = require('./reflectApply');\n\n/** @type {import('./actualApply')} */\nmodule.exports = $reflectApply || bind.call($call, $apply);\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,eAAe,CAAC;AAEnC,IAAIC,MAAM,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AACvC,IAAIE,KAAK,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AACrC,IAAIG,aAAa,GAAGH,OAAO,CAAC,gBAAgB,CAAC;;AAE7C;AACAI,MAAM,CAACC,OAAO,GAAGF,aAAa,IAAIJ,IAAI,CAACO,IAAI,CAACJ,KAAK,EAAED,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}