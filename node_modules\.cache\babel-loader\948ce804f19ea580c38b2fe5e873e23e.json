{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\FactoryExpenses.js\";\nimport React, { useState } from 'react';\nconst FactoryExpenses = () => {\n  const [expenses, setExpenses] = useState([{\n    id: 1,\n    date: '2024-01-15',\n    expenseCategory: 'صيانة',\n    description: 'صيانة دورية للآلات',\n    amount: 5500,\n    department: 'الإنتاج',\n    approvedBy: 'مدير المصنع',\n    receiptNumber: 'FAC-001'\n  }]);\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factory-expenses\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    className: \"card-title\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 11\n    }\n  }, \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0645\\u0635\\u0646\\u0639\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 11\n    }\n  }, \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0635\\u0631\\u0648\\u0641 \\u0645\\u0635\\u0646\\u0639\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 17\n    }\n  }, \"\\u0641\\u0626\\u0629 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u0648\\u0635\\u0641\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u0642\\u0633\\u0645\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 17\n    }\n  }, \"\\u0645\\u0639\\u062A\\u0645\\u062F \\u0645\\u0646\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 17\n    }\n  }, \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0625\\u064A\\u0635\\u0627\\u0644\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 17\n    }\n  }, \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }\n  }, expenses.map(expense => /*#__PURE__*/React.createElement(\"tr\", {\n    key: expense.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 19\n    }\n  }, new Date(expense.date).toLocaleDateString('ar-SA')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 19\n    }\n  }, expense.expenseCategory), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 19\n    }\n  }, expense.description), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      fontWeight: 'bold',\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 19\n    }\n  }, formatCurrency(expense.amount)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 19\n    }\n  }, expense.department), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 19\n    }\n  }, expense.approvedBy), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 19\n    }\n  }, expense.receiptNumber), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-secondary\",\n    style: {\n      padding: '5px 10px',\n      fontSize: '12px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 21\n    }\n  }, \"\\u270F\\uFE0F \\u062A\\u0639\\u062F\\u064A\\u0644\")))))))));\n};\nexport default FactoryExpenses;", "map": {"version": 3, "names": ["React", "useState", "FactoryExpenses", "expenses", "setExpenses", "id", "date", "expenseCategory", "description", "amount", "department", "approvedBy", "receiptNumber", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "format", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "expense", "key", "Date", "toLocaleDateString", "fontWeight", "color", "padding", "fontSize"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/FactoryExpenses.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst FactoryExpenses = () => {\n  const [expenses, setExpenses] = useState([\n    {\n      id: 1,\n      date: '2024-01-15',\n      expenseCategory: 'صيانة',\n      description: 'صيانة دورية للآلات',\n      amount: 5500,\n      department: 'الإنتاج',\n      approvedBy: 'مدير المصنع',\n      receiptNumber: 'FAC-001'\n    }\n  ]);\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  return (\n    <div className=\"factory-expenses\">\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">مصروفات المصنع</h3>\n          <button className=\"btn btn-primary\">\n            ➕ إضافة مصروف مصنع\n          </button>\n        </div>\n\n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>التاريخ</th>\n                <th>فئة المصروف</th>\n                <th>الوصف</th>\n                <th>المبلغ</th>\n                <th>القسم</th>\n                <th>معتمد من</th>\n                <th>رقم الإيصال</th>\n                <th>إجراءات</th>\n              </tr>\n            </thead>\n            <tbody>\n              {expenses.map((expense) => (\n                <tr key={expense.id}>\n                  <td>{new Date(expense.date).toLocaleDateString('ar-SA')}</td>\n                  <td>{expense.expenseCategory}</td>\n                  <td>{expense.description}</td>\n                  <td style={{ fontWeight: 'bold', color: '#dc3545' }}>\n                    {formatCurrency(expense.amount)}\n                  </td>\n                  <td>{expense.department}</td>\n                  <td>{expense.approvedBy}</td>\n                  <td>{expense.receiptNumber}</td>\n                  <td>\n                    <button className=\"btn btn-secondary\" style={{ padding: '5px 10px', fontSize: '12px' }}>\n                      ✏️ تعديل\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FactoryExpenses;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAEvC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGH,QAAQ,CAAC,CACvC;IACEI,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,eAAe,EAAE,OAAO;IACxBC,WAAW,EAAE,oBAAoB;IACjCC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE,aAAa;IACzBC,aAAa,EAAE;EACjB,CAAC,CACF,CAAC;EAEF,MAAMC,cAAc,GAAIJ,MAAM,IAAK;IACjC,OAAO,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACT,MAAM,CAAC;EACnB,CAAC;EAED,oBACET,KAAA,CAAAmB,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/B1B,KAAA,CAAAmB,aAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnB1B,KAAA,CAAAmB,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B1B,KAAA,CAAAmB,aAAA;IAAIC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,iFAAkB,CAAC,eAC9C1B,KAAA,CAAAmB,aAAA;IAAQC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+FAE5B,CACL,CAAC,eAEN1B,KAAA,CAAAmB,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B1B,KAAA,CAAAmB,aAAA;IAAOC,SAAS,EAAC,OAAO;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtB1B,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE1B,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE1B,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,4CAAW,CAAC,eAChB1B,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,+DAAe,CAAC,eACpB1B,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,gCAAS,CAAC,eACd1B,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sCAAU,CAAC,eACf1B,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,gCAAS,CAAC,eACd1B,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,6CAAY,CAAC,eACjB1B,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,+DAAe,CAAC,eACpB1B,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,4CAAW,CACb,CACC,CAAC,eACR1B,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGvB,QAAQ,CAACwB,GAAG,CAAEC,OAAO,iBACpB5B,KAAA,CAAAmB,aAAA;IAAIU,GAAG,EAAED,OAAO,CAACvB,EAAG;IAAAgB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClB1B,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,IAAII,IAAI,CAACF,OAAO,CAACtB,IAAI,CAAC,CAACyB,kBAAkB,CAAC,OAAO,CAAM,CAAC,eAC7D/B,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKE,OAAO,CAACrB,eAAoB,CAAC,eAClCP,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKE,OAAO,CAACpB,WAAgB,CAAC,eAC9BR,KAAA,CAAAmB,aAAA;IAAIH,KAAK,EAAE;MAAEgB,UAAU,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjDb,cAAc,CAACe,OAAO,CAACnB,MAAM,CAC5B,CAAC,eACLT,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKE,OAAO,CAAClB,UAAe,CAAC,eAC7BV,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKE,OAAO,CAACjB,UAAe,CAAC,eAC7BX,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKE,OAAO,CAAChB,aAAkB,CAAC,eAChCZ,KAAA,CAAAmB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE1B,KAAA,CAAAmB,aAAA;IAAQC,SAAS,EAAC,mBAAmB;IAACJ,KAAK,EAAE;MAAEkB,OAAO,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAO,CAAE;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6CAEhF,CACN,CACF,CACL,CACI,CACF,CACJ,CACF,CACF,CAAC;AAEV,CAAC;AAED,eAAexB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}