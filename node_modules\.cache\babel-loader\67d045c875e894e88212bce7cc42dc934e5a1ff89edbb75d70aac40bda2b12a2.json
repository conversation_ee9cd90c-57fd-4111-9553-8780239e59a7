{"ast": null, "code": "'use strict';\n\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar id = 0;\nvar postfix = Math.random();\nvar toString = uncurryThis(1.0.toString);\nmodule.exports = function (key) {\n  return 'Symbol(' + (key === undefined ? '' : key) + ')_' + toString(++id + postfix, 36);\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}