{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\WarehousePurchases.js\";\nimport React, { useState, useEffect } from 'react';\nimport { formatCurrency } from '../utils/currency';\nconst WarehousePurchases = () => {\n  const [purchases, setPurchases] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    itemName: '',\n    quantity: '',\n    unitPrice: '',\n    supplier: '',\n    invoiceNumber: '',\n    notes: ''\n  });\n  useEffect(() => {\n    // بيانات تجريبية محدثة\n    setPurchases([{\n      id: 1,\n      date: '2024-01-15',\n      itemName: 'مواد خام - حديد مقاوم للصدأ',\n      quantity: 100,\n      unitPrice: 85,\n      totalPrice: 8500,\n      supplier: 'شركة الحديد المتحدة',\n      invoiceNumber: 'INV-001',\n      notes: 'جودة عالية - مقاوم للصدأ',\n      category: 'مواد خام',\n      unit: 'كيلو'\n    }, {\n      id: 2,\n      date: '2024-01-14',\n      itemName: 'أدوات تصنيع متقدمة',\n      quantity: 25,\n      unitPrice: 120,\n      totalPrice: 3000,\n      supplier: 'مؤسسة الأدوات الصناعية',\n      invoiceNumber: 'INV-002',\n      notes: 'أدوات عالية الدقة',\n      category: 'أدوات',\n      unit: 'قطعة'\n    }, {\n      id: 3,\n      date: '2024-01-13',\n      itemName: 'مواد كيميائية للطلاء',\n      quantity: 50,\n      unitPrice: 45,\n      totalPrice: 2250,\n      supplier: 'شركة الكيماويات المتخصصة',\n      invoiceNumber: 'INV-003',\n      notes: 'مواد صديقة للبيئة',\n      category: 'كيماويات',\n      unit: 'لتر'\n    }, {\n      id: 4,\n      date: '2024-01-12',\n      itemName: 'قطع غيار آلات',\n      quantity: 15,\n      unitPrice: 200,\n      totalPrice: 3000,\n      supplier: 'مركز قطع الغيار',\n      invoiceNumber: 'INV-004',\n      notes: 'قطع أصلية مضمونة',\n      category: 'قطع غيار',\n      unit: 'قطعة'\n    }, {\n      id: 5,\n      date: '2024-01-11',\n      itemName: 'مواد تعبئة وتغليف',\n      quantity: 200,\n      unitPrice: 12,\n      totalPrice: 2400,\n      supplier: 'شركة التعبئة الحديثة',\n      invoiceNumber: 'INV-005',\n      notes: 'مواد قابلة للتدوير',\n      category: 'تعبئة',\n      unit: 'وحدة'\n    }]);\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    const newPurchase = {\n      id: purchases.length + 1,\n      ...formData,\n      quantity: parseInt(formData.quantity),\n      unitPrice: parseFloat(formData.unitPrice),\n      totalPrice: parseInt(formData.quantity) * parseFloat(formData.unitPrice)\n    };\n    setPurchases(prev => [newPurchase, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      itemName: '',\n      quantity: '',\n      unitPrice: '',\n      supplier: '',\n      invoiceNumber: '',\n      notes: ''\n    });\n    setShowForm(false);\n  };\n  const getTotalValue = () => {\n    return purchases.reduce((sum, purchase) => sum + purchase.totalPrice, 0);\n  };\n  const getTotalItems = () => {\n    return purchases.reduce((sum, purchase) => sum + purchase.quantity, 0);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"warehouse-purchases\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stats-grid\",\n    style: {\n      marginBottom: '30px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-value\",\n    style: {\n      color: '#1e3a8a'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 11\n    }\n  }, formatCurrency(getTotalValue())), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 11\n    }\n  }, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-value\",\n    style: {\n      color: '#ffa500'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 11\n    }\n  }, getTotalItems()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 11\n    }\n  }, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-value\",\n    style: {\n      color: '#ea580c'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 11\n    }\n  }, purchases.length), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 11\n    }\n  }, \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-value\",\n    style: {\n      color: '#1e40af'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 11\n    }\n  }, new Set(purchases.map(p => p.supplier)).size), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 11\n    }\n  }, \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    className: \"card-title\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 11\n    }\n  }, \"\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0646\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    onClick: () => setShowForm(!showForm),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 11\n    }\n  }, \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A \\u062C\\u062F\\u064A\\u062F\\u0629\")), showForm && /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    style: {\n      marginBottom: '20px',\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-row\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"date\",\n    name: \"date\",\n    value: formData.date,\n    onChange: handleInputChange,\n    className: \"form-input\",\n    required: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 17\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 17\n    }\n  }, \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    name: \"invoiceNumber\",\n    value: formData.invoiceNumber,\n    onChange: handleInputChange,\n    className: \"form-input\",\n    placeholder: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 17\n    }\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 15\n    }\n  }, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0635\\u0646\\u0641\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    name: \"itemName\",\n    value: formData.itemName,\n    onChange: handleInputChange,\n    className: \"form-input\",\n    placeholder: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0635\\u0646\\u0641 \\u0623\\u0648 \\u0627\\u0644\\u0645\\u0627\\u062F\\u0629\",\n    required: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-row\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"number\",\n    name: \"quantity\",\n    value: formData.quantity,\n    onChange: handleInputChange,\n    className: \"form-input\",\n    placeholder: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\",\n    required: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 17\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 17\n    }\n  }, \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"number\",\n    name: \"unitPrice\",\n    value: formData.unitPrice,\n    onChange: handleInputChange,\n    className: \"form-input\",\n    placeholder: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\",\n    step: \"0.01\",\n    required: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 17\n    }\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 15\n    }\n  }, \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    name: \"supplier\",\n    value: formData.supplier,\n    onChange: handleInputChange,\n    className: \"form-input\",\n    placeholder: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 15\n    }\n  }, \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"), /*#__PURE__*/React.createElement(\"textarea\", {\n    name: \"notes\",\n    value: formData.notes,\n    onChange: handleInputChange,\n    className: \"form-input\",\n    placeholder: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629\",\n    rows: \"3\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 15\n    }\n  })), formData.quantity && formData.unitPrice && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"alert alert-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 17\n    }\n  }, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u0644\\u063A: \", formatCurrency(formData.quantity * formData.unitPrice))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 15\n    }\n  }, \"\\uD83D\\uDCBE \\u062D\\u0641\\u0638 \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"btn btn-secondary\",\n    onClick: () => setShowForm(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 15\n    }\n  }, \"\\u274C \\u0625\\u0644\\u063A\\u0627\\u0621\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0635\\u0646\\u0641\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 17\n    }\n  }, \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 17\n    }\n  }, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 17\n    }\n  }, \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 17\n    }\n  }, \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 13\n    }\n  }, purchases.map(purchase => /*#__PURE__*/React.createElement(\"tr\", {\n    key: purchase.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 19\n    }\n  }, new Date(purchase.date).toLocaleDateString('ar-SA')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 19\n    }\n  }, purchase.itemName), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 19\n    }\n  }, purchase.quantity, \" \", purchase.unit), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 19\n    }\n  }, formatCurrency(purchase.unitPrice)), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      fontWeight: 'bold',\n      color: '#1e3a8a'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 19\n    }\n  }, formatCurrency(purchase.totalPrice)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 19\n    }\n  }, purchase.supplier), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 19\n    }\n  }, purchase.invoiceNumber), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-secondary\",\n    style: {\n      padding: '5px 10px',\n      fontSize: '12px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 21\n    }\n  }, \"\\u270F\\uFE0F \\u062A\\u0639\\u062F\\u064A\\u0644\")))))))));\n};\nexport default WarehousePurchases;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "formatCurrency", "WarehousePurchases", "purchases", "setPurchases", "showForm", "setShowForm", "formData", "setFormData", "date", "Date", "toISOString", "split", "itemName", "quantity", "unitPrice", "supplier", "invoiceNumber", "notes", "id", "totalPrice", "category", "unit", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "newPurchase", "length", "parseInt", "parseFloat", "getTotalValue", "reduce", "sum", "purchase", "getTotalItems", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginBottom", "color", "Set", "map", "p", "size", "onClick", "onSubmit", "padding", "backgroundColor", "borderRadius", "type", "onChange", "required", "placeholder", "step", "rows", "display", "gap", "key", "toLocaleDateString", "fontWeight", "fontSize"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/WarehousePurchases.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { formatCurrency } from '../utils/currency';\n\nconst WarehousePurchases = () => {\n  const [purchases, setPurchases] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    itemName: '',\n    quantity: '',\n    unitPrice: '',\n    supplier: '',\n    invoiceNumber: '',\n    notes: ''\n  });\n\n  useEffect(() => {\n    // بيانات تجريبية محدثة\n    setPurchases([\n      {\n        id: 1,\n        date: '2024-01-15',\n        itemName: 'مواد خام - حديد مقاوم للصدأ',\n        quantity: 100,\n        unitPrice: 85,\n        totalPrice: 8500,\n        supplier: 'شركة الحديد المتحدة',\n        invoiceNumber: 'INV-001',\n        notes: 'جودة عالية - مقاوم للصدأ',\n        category: 'مواد خام',\n        unit: 'كيلو'\n      },\n      {\n        id: 2,\n        date: '2024-01-14',\n        itemName: 'أدوات تصنيع متقدمة',\n        quantity: 25,\n        unitPrice: 120,\n        totalPrice: 3000,\n        supplier: 'مؤسسة الأدوات الصناعية',\n        invoiceNumber: 'INV-002',\n        notes: 'أدوات عالية الدقة',\n        category: 'أدوات',\n        unit: 'قطعة'\n      },\n      {\n        id: 3,\n        date: '2024-01-13',\n        itemName: 'مواد كيميائية للطلاء',\n        quantity: 50,\n        unitPrice: 45,\n        totalPrice: 2250,\n        supplier: 'شركة الكيماويات المتخصصة',\n        invoiceNumber: 'INV-003',\n        notes: 'مواد صديقة للبيئة',\n        category: 'كيماويات',\n        unit: 'لتر'\n      },\n      {\n        id: 4,\n        date: '2024-01-12',\n        itemName: 'قطع غيار آلات',\n        quantity: 15,\n        unitPrice: 200,\n        totalPrice: 3000,\n        supplier: 'مركز قطع الغيار',\n        invoiceNumber: 'INV-004',\n        notes: 'قطع أصلية مضمونة',\n        category: 'قطع غيار',\n        unit: 'قطعة'\n      },\n      {\n        id: 5,\n        date: '2024-01-11',\n        itemName: 'مواد تعبئة وتغليف',\n        quantity: 200,\n        unitPrice: 12,\n        totalPrice: 2400,\n        supplier: 'شركة التعبئة الحديثة',\n        invoiceNumber: 'INV-005',\n        notes: 'مواد قابلة للتدوير',\n        category: 'تعبئة',\n        unit: 'وحدة'\n      }\n    ]);\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    const newPurchase = {\n      id: purchases.length + 1,\n      ...formData,\n      quantity: parseInt(formData.quantity),\n      unitPrice: parseFloat(formData.unitPrice),\n      totalPrice: parseInt(formData.quantity) * parseFloat(formData.unitPrice)\n    };\n\n    setPurchases(prev => [newPurchase, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      itemName: '',\n      quantity: '',\n      unitPrice: '',\n      supplier: '',\n      invoiceNumber: '',\n      notes: ''\n    });\n    setShowForm(false);\n  };\n\n\n\n  const getTotalValue = () => {\n    return purchases.reduce((sum, purchase) => sum + purchase.totalPrice, 0);\n  };\n\n  const getTotalItems = () => {\n    return purchases.reduce((sum, purchase) => sum + purchase.quantity, 0);\n  };\n\n  return (\n    <div className=\"warehouse-purchases\">\n      {/* إحصائيات المخزن */}\n      <div className=\"stats-grid\" style={{ marginBottom: '30px' }}>\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#1e3a8a' }}>\n            {formatCurrency(getTotalValue())}\n          </div>\n          <div className=\"stat-label\">إجمالي قيمة المشتريات</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#ffa500' }}>\n            {getTotalItems()}\n          </div>\n          <div className=\"stat-label\">إجمالي الكمية</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#ea580c' }}>\n            {purchases.length}\n          </div>\n          <div className=\"stat-label\">عدد المشتريات</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#1e40af' }}>\n            {new Set(purchases.map(p => p.supplier)).size}\n          </div>\n          <div className=\"stat-label\">عدد الموردين</div>\n        </div>\n      </div>\n\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">مشتريات المخزن</h3>\n          <button \n            className=\"btn btn-primary\"\n            onClick={() => setShowForm(!showForm)}\n          >\n            ➕ إضافة مشتريات جديدة\n          </button>\n        </div>\n\n        {/* نموذج إضافة مشتريات */}\n        {showForm && (\n          <form onSubmit={handleSubmit} style={{ marginBottom: '20px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">التاريخ</label>\n                <input\n                  type=\"date\"\n                  name=\"date\"\n                  value={formData.date}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  required\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">رقم الفاتورة</label>\n                <input\n                  type=\"text\"\n                  name=\"invoiceNumber\"\n                  value={formData.invoiceNumber}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"رقم الفاتورة\"\n                />\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">اسم الصنف</label>\n              <input\n                type=\"text\"\n                name=\"itemName\"\n                value={formData.itemName}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"اسم الصنف أو المادة\"\n                required\n              />\n            </div>\n\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">الكمية</label>\n                <input\n                  type=\"number\"\n                  name=\"quantity\"\n                  value={formData.quantity}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"الكمية\"\n                  required\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">سعر الوحدة</label>\n                <input\n                  type=\"number\"\n                  name=\"unitPrice\"\n                  value={formData.unitPrice}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"سعر الوحدة\"\n                  step=\"0.01\"\n                  required\n                />\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">المورد</label>\n              <input\n                type=\"text\"\n                name=\"supplier\"\n                value={formData.supplier}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"اسم المورد\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">ملاحظات</label>\n              <textarea\n                name=\"notes\"\n                value={formData.notes}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"ملاحظات إضافية\"\n                rows=\"3\"\n              />\n            </div>\n\n            {formData.quantity && formData.unitPrice && (\n              <div className=\"alert alert-info\">\n                <strong>إجمالي المبلغ: {formatCurrency(formData.quantity * formData.unitPrice)}</strong>\n              </div>\n            )}\n\n            <div style={{ display: 'flex', gap: '10px' }}>\n              <button type=\"submit\" className=\"btn btn-primary\">\n                💾 حفظ المشتريات\n              </button>\n              <button \n                type=\"button\" \n                className=\"btn btn-secondary\"\n                onClick={() => setShowForm(false)}\n              >\n                ❌ إلغاء\n              </button>\n            </div>\n          </form>\n        )}\n\n        {/* جدول المشتريات */}\n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>التاريخ</th>\n                <th>اسم الصنف</th>\n                <th>الكمية</th>\n                <th>سعر الوحدة</th>\n                <th>إجمالي المبلغ</th>\n                <th>المورد</th>\n                <th>رقم الفاتورة</th>\n                <th>إجراءات</th>\n              </tr>\n            </thead>\n            <tbody>\n              {purchases.map((purchase) => (\n                <tr key={purchase.id}>\n                  <td>{new Date(purchase.date).toLocaleDateString('ar-SA')}</td>\n                  <td>{purchase.itemName}</td>\n                  <td>{purchase.quantity} {purchase.unit}</td>\n                  <td>{formatCurrency(purchase.unitPrice)}</td>\n                  <td style={{ fontWeight: 'bold', color: '#1e3a8a' }}>\n                    {formatCurrency(purchase.totalPrice)}\n                  </td>\n                  <td>{purchase.supplier}</td>\n                  <td>{purchase.invoiceNumber}</td>\n                  <td>\n                    <button className=\"btn btn-secondary\" style={{ padding: '5px 10px', fontSize: '12px' }}>\n                      ✏️ تعديل\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default WarehousePurchases;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,mBAAmB;AAElD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGL,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACM,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5CC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEFlB,SAAS,CAAC,MAAM;IACd;IACAI,YAAY,CAAC,CACX;MACEe,EAAE,EAAE,CAAC;MACLV,IAAI,EAAE,YAAY;MAClBI,QAAQ,EAAE,6BAA6B;MACvCC,QAAQ,EAAE,GAAG;MACbC,SAAS,EAAE,EAAE;MACbK,UAAU,EAAE,IAAI;MAChBJ,QAAQ,EAAE,qBAAqB;MAC/BC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE,0BAA0B;MACjCG,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLV,IAAI,EAAE,YAAY;MAClBI,QAAQ,EAAE,oBAAoB;MAC9BC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,GAAG;MACdK,UAAU,EAAE,IAAI;MAChBJ,QAAQ,EAAE,wBAAwB;MAClCC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE,mBAAmB;MAC1BG,QAAQ,EAAE,OAAO;MACjBC,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLV,IAAI,EAAE,YAAY;MAClBI,QAAQ,EAAE,sBAAsB;MAChCC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbK,UAAU,EAAE,IAAI;MAChBJ,QAAQ,EAAE,0BAA0B;MACpCC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE,mBAAmB;MAC1BG,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLV,IAAI,EAAE,YAAY;MAClBI,QAAQ,EAAE,eAAe;MACzBC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,GAAG;MACdK,UAAU,EAAE,IAAI;MAChBJ,QAAQ,EAAE,iBAAiB;MAC3BC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE,kBAAkB;MACzBG,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE;IACR,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLV,IAAI,EAAE,YAAY;MAClBI,QAAQ,EAAE,mBAAmB;MAC7BC,QAAQ,EAAE,GAAG;MACbC,SAAS,EAAE,EAAE;MACbK,UAAU,EAAE,IAAI;MAChBJ,QAAQ,EAAE,sBAAsB;MAChCC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE,oBAAoB;MAC3BG,QAAQ,EAAE,OAAO;MACjBC,IAAI,EAAE;IACR,CAAC,CACF,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCnB,WAAW,CAACoB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAIL,CAAC,IAAK;IAC1BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,MAAMC,WAAW,GAAG;MAClBZ,EAAE,EAAEhB,SAAS,CAAC6B,MAAM,GAAG,CAAC;MACxB,GAAGzB,QAAQ;MACXO,QAAQ,EAAEmB,QAAQ,CAAC1B,QAAQ,CAACO,QAAQ,CAAC;MACrCC,SAAS,EAAEmB,UAAU,CAAC3B,QAAQ,CAACQ,SAAS,CAAC;MACzCK,UAAU,EAAEa,QAAQ,CAAC1B,QAAQ,CAACO,QAAQ,CAAC,GAAGoB,UAAU,CAAC3B,QAAQ,CAACQ,SAAS;IACzE,CAAC;IAEDX,YAAY,CAACwB,IAAI,IAAI,CAACG,WAAW,EAAE,GAAGH,IAAI,CAAC,CAAC;IAC5CpB,WAAW,CAAC;MACVC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBC,KAAK,EAAE;IACT,CAAC,CAAC;IACFZ,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAID,MAAM6B,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOhC,SAAS,CAACiC,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKD,GAAG,GAAGC,QAAQ,CAAClB,UAAU,EAAE,CAAC,CAAC;EAC1E,CAAC;EAED,MAAMmB,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOpC,SAAS,CAACiC,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKD,GAAG,GAAGC,QAAQ,CAACxB,QAAQ,EAAE,CAAC,CAAC;EACxE,CAAC;EAED,oBACEhB,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElCjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEC,YAAY,EAAE;IAAO,CAAE;IAAAP,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1DjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACrD9C,cAAc,CAACkC,aAAa,CAAC,CAAC,CAC5B,CAAC,eACNrC,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sHAA0B,CACnD,CAAC,eAENjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACrDR,aAAa,CAAC,CACZ,CAAC,eACNzC,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2EAAkB,CAC3C,CAAC,eAENjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACrD5C,SAAS,CAAC6B,MACR,CAAC,eACNlC,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2EAAkB,CAC3C,CAAC,eAENjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACrD,IAAII,GAAG,CAAChD,SAAS,CAACiD,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACrC,QAAQ,CAAC,CAAC,CAACsC,IACtC,CAAC,eACNxD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,qEAAiB,CAC1C,CACF,CAAC,eAENjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BjD,KAAA,CAAA0C,aAAA;IAAIC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,iFAAkB,CAAC,eAC9CjD,KAAA,CAAA0C,aAAA;IACEC,SAAS,EAAC,iBAAiB;IAC3Bc,OAAO,EAAEA,CAAA,KAAMjD,WAAW,CAAC,CAACD,QAAQ,CAAE;IAAAqC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvC,iHAEO,CACL,CAAC,EAGL1C,QAAQ,iBACPP,KAAA,CAAA0C,aAAA;IAAMgB,QAAQ,EAAE3B,YAAa;IAACmB,KAAK,EAAE;MAAEC,YAAY,EAAE,MAAM;MAAEQ,OAAO,EAAE,MAAM;MAAEC,eAAe,EAAE,SAAS;MAAEC,YAAY,EAAE;IAAM,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9HjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBjD,KAAA,CAAA0C,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,4CAAc,CAAC,eAC7CjD,KAAA,CAAA0C,aAAA;IACEoB,IAAI,EAAC,MAAM;IACXnC,IAAI,EAAC,MAAM;IACXC,KAAK,EAAEnB,QAAQ,CAACE,IAAK;IACrBoD,QAAQ,EAAEtC,iBAAkB;IAC5BkB,SAAS,EAAC,YAAY;IACtBqB,QAAQ;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACT,CACE,CAAC,eAENjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBjD,KAAA,CAAA0C,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,qEAAmB,CAAC,eAClDjD,KAAA,CAAA0C,aAAA;IACEoB,IAAI,EAAC,MAAM;IACXnC,IAAI,EAAC,eAAe;IACpBC,KAAK,EAAEnB,QAAQ,CAACU,aAAc;IAC9B4C,QAAQ,EAAEtC,iBAAkB;IAC5BkB,SAAS,EAAC,YAAY;IACtBsB,WAAW,EAAC,qEAAc;IAAArB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC3B,CACE,CACF,CAAC,eAENjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBjD,KAAA,CAAA0C,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mDAAgB,CAAC,eAC/CjD,KAAA,CAAA0C,aAAA;IACEoB,IAAI,EAAC,MAAM;IACXnC,IAAI,EAAC,UAAU;IACfC,KAAK,EAAEnB,QAAQ,CAACM,QAAS;IACzBgD,QAAQ,EAAEtC,iBAAkB;IAC5BkB,SAAS,EAAC,YAAY;IACtBsB,WAAW,EAAC,qGAAqB;IACjCD,QAAQ;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACT,CACE,CAAC,eAENjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBjD,KAAA,CAAA0C,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sCAAa,CAAC,eAC5CjD,KAAA,CAAA0C,aAAA;IACEoB,IAAI,EAAC,QAAQ;IACbnC,IAAI,EAAC,UAAU;IACfC,KAAK,EAAEnB,QAAQ,CAACO,QAAS;IACzB+C,QAAQ,EAAEtC,iBAAkB;IAC5BkB,SAAS,EAAC,YAAY;IACtBsB,WAAW,EAAC,sCAAQ;IACpBD,QAAQ;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACT,CACE,CAAC,eAENjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBjD,KAAA,CAAA0C,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yDAAiB,CAAC,eAChDjD,KAAA,CAAA0C,aAAA;IACEoB,IAAI,EAAC,QAAQ;IACbnC,IAAI,EAAC,WAAW;IAChBC,KAAK,EAAEnB,QAAQ,CAACQ,SAAU;IAC1B8C,QAAQ,EAAEtC,iBAAkB;IAC5BkB,SAAS,EAAC,YAAY;IACtBsB,WAAW,EAAC,yDAAY;IACxBC,IAAI,EAAC,MAAM;IACXF,QAAQ;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACT,CACE,CACF,CAAC,eAENjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBjD,KAAA,CAAA0C,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sCAAa,CAAC,eAC5CjD,KAAA,CAAA0C,aAAA;IACEoB,IAAI,EAAC,MAAM;IACXnC,IAAI,EAAC,UAAU;IACfC,KAAK,EAAEnB,QAAQ,CAACS,QAAS;IACzB6C,QAAQ,EAAEtC,iBAAkB;IAC5BkB,SAAS,EAAC,YAAY;IACtBsB,WAAW,EAAC,yDAAY;IAAArB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACzB,CACE,CAAC,eAENjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBjD,KAAA,CAAA0C,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,4CAAc,CAAC,eAC7CjD,KAAA,CAAA0C,aAAA;IACEf,IAAI,EAAC,OAAO;IACZC,KAAK,EAAEnB,QAAQ,CAACW,KAAM;IACtB2C,QAAQ,EAAEtC,iBAAkB;IAC5BkB,SAAS,EAAC,YAAY;IACtBsB,WAAW,EAAC,iFAAgB;IAC5BE,IAAI,EAAC,GAAG;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACT,CACE,CAAC,EAELxC,QAAQ,CAACO,QAAQ,IAAIP,QAAQ,CAACQ,SAAS,iBACtCjB,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BjD,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQ,6EAAe,EAAC9C,cAAc,CAACM,QAAQ,CAACO,QAAQ,GAAGP,QAAQ,CAACQ,SAAS,CAAU,CACpF,CACN,eAEDjB,KAAA,CAAA0C,aAAA;IAAKQ,KAAK,EAAE;MAAEkB,OAAO,EAAE,MAAM;MAAEC,GAAG,EAAE;IAAO,CAAE;IAAAzB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3CjD,KAAA,CAAA0C,aAAA;IAAQoB,IAAI,EAAC,QAAQ;IAACnB,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,wFAE1C,CAAC,eACTjD,KAAA,CAAA0C,aAAA;IACEoB,IAAI,EAAC,QAAQ;IACbnB,SAAS,EAAC,mBAAmB;IAC7Bc,OAAO,EAAEA,CAAA,KAAMjD,WAAW,CAAC,KAAK,CAAE;IAAAoC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACnC,uCAEO,CACL,CACD,CACP,eAGDjD,KAAA,CAAA0C,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BjD,KAAA,CAAA0C,aAAA;IAAOC,SAAS,EAAC,OAAO;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBjD,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjD,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjD,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,4CAAW,CAAC,eAChBjD,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mDAAa,CAAC,eAClBjD,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sCAAU,CAAC,eACfjD,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yDAAc,CAAC,eACnBjD,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,2EAAiB,CAAC,eACtBjD,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sCAAU,CAAC,eACfjD,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qEAAgB,CAAC,eACrBjD,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,4CAAW,CACb,CACC,CAAC,eACRjD,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG5C,SAAS,CAACiD,GAAG,CAAEd,QAAQ,iBACtBxC,KAAA,CAAA0C,aAAA;IAAI4B,GAAG,EAAE9B,QAAQ,CAACnB,EAAG;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBjD,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,IAAIrC,IAAI,CAAC4B,QAAQ,CAAC7B,IAAI,CAAC,CAAC4D,kBAAkB,CAAC,OAAO,CAAM,CAAC,eAC9DvE,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKT,QAAQ,CAACzB,QAAa,CAAC,eAC5Bf,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKT,QAAQ,CAACxB,QAAQ,EAAC,GAAC,EAACwB,QAAQ,CAAChB,IAAS,CAAC,eAC5CxB,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK9C,cAAc,CAACqC,QAAQ,CAACvB,SAAS,CAAM,CAAC,eAC7CjB,KAAA,CAAA0C,aAAA;IAAIQ,KAAK,EAAE;MAAEsB,UAAU,EAAE,MAAM;MAAEpB,KAAK,EAAE;IAAU,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjD9C,cAAc,CAACqC,QAAQ,CAAClB,UAAU,CACjC,CAAC,eACLtB,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKT,QAAQ,CAACtB,QAAa,CAAC,eAC5BlB,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKT,QAAQ,CAACrB,aAAkB,CAAC,eACjCnB,KAAA,CAAA0C,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEjD,KAAA,CAAA0C,aAAA;IAAQC,SAAS,EAAC,mBAAmB;IAACO,KAAK,EAAE;MAAES,OAAO,EAAE,UAAU;MAAEc,QAAQ,EAAE;IAAO,CAAE;IAAA7B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6CAEhF,CACN,CACF,CACL,CACI,CACF,CACJ,CACF,CACF,CAAC;AAEV,CAAC;AAED,eAAe7C,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}