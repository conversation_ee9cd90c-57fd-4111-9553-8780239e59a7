import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { formatCurrency, formatDate } from '../utils/currency';

const Dashboard = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalIncome: 0,
    totalExpenses: 0,
    netProfit: 0,
    pendingWithdrawals: 0,
    warehouseValue: 0,
    treasuryBalance: 0
  });

  const [recentTransactions, setRecentTransactions] = useState([]);

  useEffect(() => {
    // هنا سيتم جلب البيانات من قاعدة البيانات
    // مؤقتاً سنستخدم بيانات تجريبية
    setStats({
      totalIncome: 150000,
      totalExpenses: 95000,
      netProfit: 55000,
      pendingWithdrawals: 12000,
      warehouseValue: 85000,
      treasuryBalance: 67000
    });

    setRecentTransactions([
      { id: 1, type: 'إيداع', description: 'إيداع نقدي', amount: 25000, date: '2024-01-15' },
      { id: 2, type: 'مصروف', description: 'مشتريات مخزن', amount: -8500, date: '2024-01-14' },
      { id: 3, type: 'مصروف', description: 'مصروفات نقل', amount: -3200, date: '2024-01-13' },
      { id: 4, type: 'سحب', description: 'سحب موظف', amount: -5000, date: '2024-01-12' },
      { id: 5, type: 'إيداع', description: 'مبيعات', amount: 18000, date: '2024-01-11' }
    ]);
  }, []);

  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('الكل');

  const filteredTransactions = recentTransactions.filter(transaction => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'الكل' || transaction.type === filterType;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="dashboard">
      {/* إحصائيات سريعة */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#ffa500' }}>
            {formatCurrency(stats.totalIncome)}
          </div>
          <div className="stat-label">إجمالي الإيرادات</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#dc3545' }}>
            {formatCurrency(stats.totalExpenses)}
          </div>
          <div className="stat-label">إجمالي المصروفات</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#1e3a8a' }}>
            {formatCurrency(stats.netProfit)}
          </div>
          <div className="stat-label">صافي الربح</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#f59e0b' }}>
            {formatCurrency(stats.pendingWithdrawals)}
          </div>
          <div className="stat-label">مسحوبات معلقة</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#1e40af' }}>
            {formatCurrency(stats.warehouseValue)}
          </div>
          <div className="stat-label">قيمة المخزن</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#ea580c' }}>
            {formatCurrency(stats.treasuryBalance)}
          </div>
          <div className="stat-label">رصيد الخزينة</div>
        </div>
      </div>

      {/* آخر المعاملات */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">آخر المعاملات</h3>
          <button className="btn btn-secondary">عرض الكل</button>
        </div>
        
        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>النوع</th>
                <th>الوصف</th>
                <th>المبلغ</th>
                <th>التاريخ</th>
              </tr>
            </thead>
            <tbody>
              {recentTransactions.map((transaction) => (
                <tr key={transaction.id}>
                  <td>
                    <span className={`badge ${transaction.type === 'إيداع' ? 'badge-success' : 
                                              transaction.type === 'مصروف' ? 'badge-danger' : 'badge-warning'}`}>
                      {transaction.type}
                    </span>
                  </td>
                  <td>{transaction.description}</td>
                  <td style={{
                    color: transaction.amount > 0 ? '#ffa500' : '#dc3545',
                    fontWeight: 'bold'
                  }}>
                    {formatCurrency(Math.abs(transaction.amount))}
                  </td>
                  <td>{formatDate(transaction.date)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* روابط سريعة */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">⚡ إجراءات سريعة</h3>
          <span className="badge badge-info">4 إجراءات متاحة</span>
        </div>

        <div className="quick-actions-grid">
          <button
            className="btn btn-primary quick-action-btn"
            onClick={() => navigate('/account-statements')}
          >
            <div className="action-icon">📋</div>
            <div className="action-text">
              <span className="action-title">إضافة معاملة جديدة</span>
              <span className="action-desc">تسجيل معاملة مالية جديدة</span>
            </div>
          </button>

          <button
            className="btn btn-success quick-action-btn"
            onClick={() => navigate('/warehouse-purchases')}
          >
            <div className="action-icon">📦</div>
            <div className="action-text">
              <span className="action-title">تسجيل مشتريات</span>
              <span className="action-desc">إضافة مشتريات للمخزن</span>
            </div>
          </button>

          <button
            className="btn btn-primary quick-action-btn"
            onClick={() => navigate('/treasury-deposits')}
          >
            <div className="action-icon">💰</div>
            <div className="action-text">
              <span className="action-title">إيداع خزينة</span>
              <span className="action-desc">تسجيل إيداع نقدي جديد</span>
            </div>
          </button>

          <button
            className="btn btn-secondary quick-action-btn"
            onClick={() => navigate('/reports')}
          >
            <div className="action-icon">📈</div>
            <div className="action-text">
              <span className="action-title">عرض التقارير</span>
              <span className="action-desc">تقارير مالية شاملة</span>
            </div>
          </button>
        </div>
      </div>

      {/* المعاملات الأخيرة */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">📋 المعاملات الأخيرة</h3>
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <input
              type="text"
              placeholder="🔍 البحث في المعاملات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ddd',
                borderRadius: '6px',
                fontSize: '14px',
                width: '200px'
              }}
            />
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ddd',
                borderRadius: '6px',
                fontSize: '14px'
              }}
            >
              <option value="الكل">جميع الأنواع</option>
              <option value="إيداع">الإيداعات</option>
              <option value="مصروف">المصروفات</option>
              <option value="سحب">المسحوبات</option>
            </select>
          </div>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>النوع</th>
                <th>الوصف</th>
                <th>المبلغ</th>
                <th>الحالة</th>
              </tr>
            </thead>
            <tbody>
              {filteredTransactions.map((transaction) => (
                <tr key={transaction.id}>
                  <td>{formatDate(transaction.date)}</td>
                  <td>
                    <span className={`badge ${
                      transaction.type === 'إيداع' ? 'badge-success' :
                      transaction.type === 'مصروف' ? 'badge-danger' : 'badge-warning'
                    }`}>
                      {transaction.type}
                    </span>
                  </td>
                  <td>{transaction.description}</td>
                  <td style={{
                    fontWeight: 'bold',
                    color: transaction.amount >= 0 ? '#28a745' : '#dc3545'
                  }}>
                    {formatCurrency(Math.abs(transaction.amount))}
                  </td>
                  <td>
                    <span className="badge badge-info">مكتمل</span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
