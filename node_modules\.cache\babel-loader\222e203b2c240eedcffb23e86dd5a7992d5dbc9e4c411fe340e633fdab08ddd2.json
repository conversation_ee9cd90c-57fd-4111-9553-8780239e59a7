{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useLocation } from 'react-router-dom';\nimport { formatDate, formatDateTime } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const location = useLocation();\n  const getPageTitle = () => {\n    const titles = {\n      '/': 'لوحة التحكم',\n      '/account-statements': 'كشف الحساب',\n      '/warehouse-purchases': 'مشتريات المخزن',\n      '/transport-expenses': 'مصروفات النقل',\n      '/living-expenses': 'مصروفات المعيشة',\n      '/paint-expenses': 'مصروفات الطلاء',\n      '/factory-expenses': 'مصروفات المصنع',\n      '/employee-withdrawals': 'مسحوبات الموظفين',\n      '/treasury-deposits': 'إيداعات الخزينة',\n      '/reports': 'التقارير'\n    };\n    return titles[location.pathname] || 'نظام المحاسبة';\n  };\n  const getCurrentDate = () => {\n    return formatDate(new Date());\n  };\n  const getCurrentTime = () => {\n    const now = new Date();\n    const hours = String(now.getHours()).padStart(2, '0');\n    const minutes = String(now.getMinutes()).padStart(2, '0');\n    return `${hours}:${minutes}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: getPageTitle()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: '14px',\n          color: '#666',\n          marginTop: '5px'\n        },\n        children: getCurrentDate()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        children: \"\\u2699\\uFE0F \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        children: \"\\uD83D\\uDCBE \\u062D\\u0641\\u0638\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useLocation", "formatDate", "formatDateTime", "jsxDEV", "_jsxDEV", "Header", "_s", "location", "getPageTitle", "titles", "pathname", "getCurrentDate", "Date", "getCurrentTime", "now", "hours", "String", "getHours", "padStart", "minutes", "getMinutes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "color", "marginTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/Header.js"], "sourcesContent": ["import React from 'react';\nimport { useLocation } from 'react-router-dom';\nimport { formatDate, formatDateTime } from '../utils/currency';\n\nconst Header = () => {\n  const location = useLocation();\n\n  const getPageTitle = () => {\n    const titles = {\n      '/': 'لوحة التحكم',\n      '/account-statements': 'كشف الحساب',\n      '/warehouse-purchases': 'مشتريات المخزن',\n      '/transport-expenses': 'مصروفات النقل',\n      '/living-expenses': 'مصروفات المعيشة',\n      '/paint-expenses': 'مصروفات الطلاء',\n      '/factory-expenses': 'مصروفات المصنع',\n      '/employee-withdrawals': 'مسحوبات الموظفين',\n      '/treasury-deposits': 'إيداعات الخزينة',\n      '/reports': 'التقارير'\n    };\n    return titles[location.pathname] || 'نظام المحاسبة';\n  };\n\n  const getCurrentDate = () => {\n    return formatDate(new Date());\n  };\n\n  const getCurrentTime = () => {\n    const now = new Date();\n    const hours = String(now.getHours()).padStart(2, '0');\n    const minutes = String(now.getMinutes()).padStart(2, '0');\n    return `${hours}:${minutes}`;\n  };\n\n  return (\n    <header className=\"header\">\n      <div>\n        <h1>{getPageTitle()}</h1>\n        <p style={{ fontSize: '14px', color: '#666', marginTop: '5px' }}>\n          {getCurrentDate()}\n        </p>\n      </div>\n      \n      <div className=\"header-actions\">\n        <button className=\"btn btn-secondary\">\n          ⚙️ الإعدادات\n        </button>\n        <button className=\"btn btn-primary\">\n          💾 حفظ\n        </button>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,EAAEC,cAAc,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG;MACb,GAAG,EAAE,aAAa;MAClB,qBAAqB,EAAE,YAAY;MACnC,sBAAsB,EAAE,gBAAgB;MACxC,qBAAqB,EAAE,eAAe;MACtC,kBAAkB,EAAE,iBAAiB;MACrC,iBAAiB,EAAE,gBAAgB;MACnC,mBAAmB,EAAE,gBAAgB;MACrC,uBAAuB,EAAE,kBAAkB;MAC3C,oBAAoB,EAAE,iBAAiB;MACvC,UAAU,EAAE;IACd,CAAC;IACD,OAAOA,MAAM,CAACF,QAAQ,CAACG,QAAQ,CAAC,IAAI,eAAe;EACrD,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOV,UAAU,CAAC,IAAIW,IAAI,CAAC,CAAC,CAAC;EAC/B,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,GAAG,GAAG,IAAIF,IAAI,CAAC,CAAC;IACtB,MAAMG,KAAK,GAAGC,MAAM,CAACF,GAAG,CAACG,QAAQ,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACrD,MAAMC,OAAO,GAAGH,MAAM,CAACF,GAAG,CAACM,UAAU,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,OAAO,GAAGH,KAAK,IAAII,OAAO,EAAE;EAC9B,CAAC;EAED,oBACEf,OAAA;IAAQiB,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACxBlB,OAAA;MAAAkB,QAAA,gBACElB,OAAA;QAAAkB,QAAA,EAAKd,YAAY,CAAC;MAAC;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACzBtB,OAAA;QAAGuB,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEC,KAAK,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAM,CAAE;QAAAR,QAAA,EAC7DX,cAAc,CAAC;MAAC;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENtB,OAAA;MAAKiB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BlB,OAAA;QAAQiB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtB,OAAA;QAAQiB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAEpC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACpB,EAAA,CAjDID,MAAM;EAAA,QACOL,WAAW;AAAA;AAAA+B,EAAA,GADxB1B,MAAM;AAmDZ,eAAeA,MAAM;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}