import React, { useState } from 'react';
import { formatCurrency, formatDate } from '../utils/currency';

const TreasuryDeposits = () => {
  const [deposits, setDeposits] = useState([
    {
      id: 1,
      date: '2024-01-15',
      depositType: 'إيداع نقدي',
      amount: 25000,
      source: 'مبيعات يومية',
      referenceNumber: 'DEP-001',
      depositedBy: 'أمين الصندوق',
      notes: 'إيداع مبيعات اليوم'
    }
  ]);



  const getTotalDeposits = () => {
    return deposits.reduce((sum, deposit) => sum + deposit.amount, 0);
  };

  return (
    <div className="treasury-deposits">
      <div className="stats-grid" style={{ marginBottom: '30px' }}>
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#ffa500' }}>
            {formatCurrency(getTotalDeposits())}
          </div>
          <div className="stat-label">إجمالي الإيداعات</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#1e3a8a' }}>
            {deposits.length}
          </div>
          <div className="stat-label">عدد الإيداعات</div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="card-title">إيداعات الخزينة</h3>
          <button className="btn btn-primary">
            ➕ إضافة إيداع جديد
          </button>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>نوع الإيداع</th>
                <th>المبلغ</th>
                <th>المصدر</th>
                <th>رقم المرجع</th>
                <th>المودع</th>
                <th>ملاحظات</th>
                <th>إجراءات</th>
              </tr>
            </thead>
            <tbody>
              {deposits.map((deposit) => (
                <tr key={deposit.id}>
                  <td>{formatDate(deposit.date)}</td>
                  <td>{deposit.depositType}</td>
                  <td style={{ fontWeight: 'bold', color: '#ffa500' }}>
                    {formatCurrency(deposit.amount)}
                  </td>
                  <td>{deposit.source}</td>
                  <td>{deposit.referenceNumber}</td>
                  <td>{deposit.depositedBy}</td>
                  <td>{deposit.notes}</td>
                  <td>
                    <button className="btn btn-secondary" style={{ padding: '5px 10px', fontSize: '12px' }}>
                      ✏️ تعديل
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TreasuryDeposits;
