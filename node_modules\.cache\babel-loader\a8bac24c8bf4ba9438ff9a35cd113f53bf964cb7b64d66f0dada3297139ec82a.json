{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { formatCurrency, formatDate } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const navigate = useNavigate();\n  const [stats, setStats] = useState({\n    totalIncome: 0,\n    totalExpenses: 0,\n    netProfit: 0,\n    pendingWithdrawals: 0,\n    warehouseValue: 0,\n    treasuryBalance: 0\n  });\n  const [recentTransactions, setRecentTransactions] = useState([]);\n  useEffect(() => {\n    // هنا سيتم جلب البيانات من قاعدة البيانات\n    // مؤقتاً سنستخدم بيانات تجريبية\n    setStats({\n      totalIncome: 150000,\n      totalExpenses: 95000,\n      netProfit: 55000,\n      pendingWithdrawals: 12000,\n      warehouseValue: 85000,\n      treasuryBalance: 67000\n    });\n    setRecentTransactions([{\n      id: 1,\n      type: 'إيداع',\n      description: 'إيداع نقدي',\n      amount: 25000,\n      date: '2024-01-15'\n    }, {\n      id: 2,\n      type: 'مصروف',\n      description: 'مشتريات مخزن',\n      amount: -8500,\n      date: '2024-01-14'\n    }, {\n      id: 3,\n      type: 'مصروف',\n      description: 'مصروفات نقل',\n      amount: -3200,\n      date: '2024-01-13'\n    }, {\n      id: 4,\n      type: 'سحب',\n      description: 'سحب موظف',\n      amount: -5000,\n      date: '2024-01-12'\n    }, {\n      id: 5,\n      type: 'إيداع',\n      description: 'مبيعات',\n      amount: 18000,\n      date: '2024-01-11'\n    }]);\n  }, []);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('الكل');\n  const filteredTransactions = recentTransactions.filter(transaction => {\n    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) || transaction.type.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterType === 'الكل' || transaction.type === filterType;\n    return matchesSearch && matchesFilter;\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#ffa500'\n          },\n          children: formatCurrency(stats.totalIncome)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0625\\u064A\\u0631\\u0627\\u062F\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#dc3545'\n          },\n          children: formatCurrency(stats.totalExpenses)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#1e3a8a'\n          },\n          children: formatCurrency(stats.netProfit)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0635\\u0627\\u0641\\u064A \\u0627\\u0644\\u0631\\u0628\\u062D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#f59e0b'\n          },\n          children: formatCurrency(stats.pendingWithdrawals)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0645\\u0633\\u062D\\u0648\\u0628\\u0627\\u062A \\u0645\\u0639\\u0644\\u0642\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#1e40af'\n          },\n          children: formatCurrency(stats.warehouseValue)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#ea580c'\n          },\n          children: formatCurrency(stats.treasuryBalance)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u062E\\u0632\\u064A\\u0646\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0622\\u062E\\u0631 \\u0627\\u0644\\u0645\\u0639\\u0627\\u0645\\u0644\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          children: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0643\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0646\\u0648\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: recentTransactions.map(transaction => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `badge ${transaction.type === 'إيداع' ? 'badge-success' : transaction.type === 'مصروف' ? 'badge-danger' : 'badge-warning'}`,\n                  children: transaction.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: transaction.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  color: transaction.amount > 0 ? '#ffa500' : '#dc3545',\n                  fontWeight: 'bold'\n                },\n                children: formatCurrency(Math.abs(transaction.amount))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatDate(transaction.date)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this)]\n            }, transaction.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u26A1 \\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0633\\u0631\\u064A\\u0639\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"badge badge-info\",\n          children: \"4 \\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0645\\u062A\\u0627\\u062D\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quick-actions-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary quick-action-btn\",\n          onClick: () => navigate('/account-statements'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-icon\",\n            children: \"\\uD83D\\uDCCB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"action-title\",\n              children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0639\\u0627\\u0645\\u0644\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"action-desc\",\n              children: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0645\\u0639\\u0627\\u0645\\u0644\\u0629 \\u0645\\u0627\\u0644\\u064A\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-success quick-action-btn\",\n          onClick: () => navigate('/warehouse-purchases'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-icon\",\n            children: \"\\uD83D\\uDCE6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"action-title\",\n              children: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"action-desc\",\n              children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A \\u0644\\u0644\\u0645\\u062E\\u0632\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary quick-action-btn\",\n          onClick: () => navigate('/treasury-deposits'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-icon\",\n            children: \"\\uD83D\\uDCB0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"action-title\",\n              children: \"\\u0625\\u064A\\u062F\\u0627\\u0639 \\u062E\\u0632\\u064A\\u0646\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"action-desc\",\n              children: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0625\\u064A\\u062F\\u0627\\u0639 \\u0646\\u0642\\u062F\\u064A \\u062C\\u062F\\u064A\\u062F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary quick-action-btn\",\n          onClick: () => navigate('/reports'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-icon\",\n            children: \"\\uD83D\\uDCC8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"action-title\",\n              children: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"action-desc\",\n              children: \"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0645\\u0627\\u0644\\u064A\\u0629 \\u0634\\u0627\\u0645\\u0644\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"gADOPePxtZDfgRf4HmeJ/5c0/j8=\", false, function () {\n  return [useNavigate];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "formatCurrency", "formatDate", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "navigate", "stats", "setStats", "totalIncome", "totalExpenses", "netProfit", "pendingWithdra<PERSON>s", "warehouseValue", "treasuryBalance", "recentTransactions", "setRecentTransactions", "id", "type", "description", "amount", "date", "searchTerm", "setSearchTerm", "filterType", "setFilterType", "filteredTransactions", "filter", "transaction", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "className", "children", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "fontWeight", "Math", "abs", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { formatCurrency, formatDate } from '../utils/currency';\n\nconst Dashboard = () => {\n  const navigate = useNavigate();\n  const [stats, setStats] = useState({\n    totalIncome: 0,\n    totalExpenses: 0,\n    netProfit: 0,\n    pendingWithdrawals: 0,\n    warehouseValue: 0,\n    treasuryBalance: 0\n  });\n\n  const [recentTransactions, setRecentTransactions] = useState([]);\n\n  useEffect(() => {\n    // هنا سيتم جلب البيانات من قاعدة البيانات\n    // مؤقتاً سنستخدم بيانات تجريبية\n    setStats({\n      totalIncome: 150000,\n      totalExpenses: 95000,\n      netProfit: 55000,\n      pendingWithdrawals: 12000,\n      warehouseValue: 85000,\n      treasuryBalance: 67000\n    });\n\n    setRecentTransactions([\n      { id: 1, type: 'إيداع', description: 'إيداع نقدي', amount: 25000, date: '2024-01-15' },\n      { id: 2, type: 'مصروف', description: 'مشتريات مخزن', amount: -8500, date: '2024-01-14' },\n      { id: 3, type: 'مصروف', description: 'مصروفات نقل', amount: -3200, date: '2024-01-13' },\n      { id: 4, type: 'سحب', description: 'سحب موظف', amount: -5000, date: '2024-01-12' },\n      { id: 5, type: 'إيداع', description: 'مبيعات', amount: 18000, date: '2024-01-11' }\n    ]);\n  }, []);\n\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('الكل');\n\n  const filteredTransactions = recentTransactions.filter(transaction => {\n    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         transaction.type.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterType === 'الكل' || transaction.type === filterType;\n    return matchesSearch && matchesFilter;\n  });\n\n  return (\n    <div className=\"dashboard\">\n      {/* إحصائيات سريعة */}\n      <div className=\"stats-grid\">\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#ffa500' }}>\n            {formatCurrency(stats.totalIncome)}\n          </div>\n          <div className=\"stat-label\">إجمالي الإيرادات</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#dc3545' }}>\n            {formatCurrency(stats.totalExpenses)}\n          </div>\n          <div className=\"stat-label\">إجمالي المصروفات</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#1e3a8a' }}>\n            {formatCurrency(stats.netProfit)}\n          </div>\n          <div className=\"stat-label\">صافي الربح</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#f59e0b' }}>\n            {formatCurrency(stats.pendingWithdrawals)}\n          </div>\n          <div className=\"stat-label\">مسحوبات معلقة</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#1e40af' }}>\n            {formatCurrency(stats.warehouseValue)}\n          </div>\n          <div className=\"stat-label\">قيمة المخزن</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#ea580c' }}>\n            {formatCurrency(stats.treasuryBalance)}\n          </div>\n          <div className=\"stat-label\">رصيد الخزينة</div>\n        </div>\n      </div>\n\n      {/* آخر المعاملات */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">آخر المعاملات</h3>\n          <button className=\"btn btn-secondary\">عرض الكل</button>\n        </div>\n        \n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>النوع</th>\n                <th>الوصف</th>\n                <th>المبلغ</th>\n                <th>التاريخ</th>\n              </tr>\n            </thead>\n            <tbody>\n              {recentTransactions.map((transaction) => (\n                <tr key={transaction.id}>\n                  <td>\n                    <span className={`badge ${transaction.type === 'إيداع' ? 'badge-success' : \n                                              transaction.type === 'مصروف' ? 'badge-danger' : 'badge-warning'}`}>\n                      {transaction.type}\n                    </span>\n                  </td>\n                  <td>{transaction.description}</td>\n                  <td style={{\n                    color: transaction.amount > 0 ? '#ffa500' : '#dc3545',\n                    fontWeight: 'bold'\n                  }}>\n                    {formatCurrency(Math.abs(transaction.amount))}\n                  </td>\n                  <td>{formatDate(transaction.date)}</td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* روابط سريعة */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">⚡ إجراءات سريعة</h3>\n          <span className=\"badge badge-info\">4 إجراءات متاحة</span>\n        </div>\n\n        <div className=\"quick-actions-grid\">\n          <button\n            className=\"btn btn-primary quick-action-btn\"\n            onClick={() => navigate('/account-statements')}\n          >\n            <div className=\"action-icon\">📋</div>\n            <div className=\"action-text\">\n              <span className=\"action-title\">إضافة معاملة جديدة</span>\n              <span className=\"action-desc\">تسجيل معاملة مالية جديدة</span>\n            </div>\n          </button>\n\n          <button\n            className=\"btn btn-success quick-action-btn\"\n            onClick={() => navigate('/warehouse-purchases')}\n          >\n            <div className=\"action-icon\">📦</div>\n            <div className=\"action-text\">\n              <span className=\"action-title\">تسجيل مشتريات</span>\n              <span className=\"action-desc\">إضافة مشتريات للمخزن</span>\n            </div>\n          </button>\n\n          <button\n            className=\"btn btn-primary quick-action-btn\"\n            onClick={() => navigate('/treasury-deposits')}\n          >\n            <div className=\"action-icon\">💰</div>\n            <div className=\"action-text\">\n              <span className=\"action-title\">إيداع خزينة</span>\n              <span className=\"action-desc\">تسجيل إيداع نقدي جديد</span>\n            </div>\n          </button>\n\n          <button\n            className=\"btn btn-secondary quick-action-btn\"\n            onClick={() => navigate('/reports')}\n          >\n            <div className=\"action-icon\">📈</div>\n            <div className=\"action-text\">\n              <span className=\"action-title\">عرض التقارير</span>\n              <span className=\"action-desc\">تقارير مالية شاملة</span>\n            </div>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,cAAc,EAAEC,UAAU,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC;IACjCY,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,SAAS,EAAE,CAAC;IACZC,kBAAkB,EAAE,CAAC;IACrBC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAEhEC,SAAS,CAAC,MAAM;IACd;IACA;IACAU,QAAQ,CAAC;MACPC,WAAW,EAAE,MAAM;MACnBC,aAAa,EAAE,KAAK;MACpBC,SAAS,EAAE,KAAK;MAChBC,kBAAkB,EAAE,KAAK;MACzBC,cAAc,EAAE,KAAK;MACrBC,eAAe,EAAE;IACnB,CAAC,CAAC;IAEFE,qBAAqB,CAAC,CACpB;MAAEC,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,WAAW,EAAE,YAAY;MAAEC,MAAM,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAa,CAAC,EACtF;MAAEJ,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,WAAW,EAAE,cAAc;MAAEC,MAAM,EAAE,CAAC,IAAI;MAAEC,IAAI,EAAE;IAAa,CAAC,EACxF;MAAEJ,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,WAAW,EAAE,aAAa;MAAEC,MAAM,EAAE,CAAC,IAAI;MAAEC,IAAI,EAAE;IAAa,CAAC,EACvF;MAAEJ,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,WAAW,EAAE,UAAU;MAAEC,MAAM,EAAE,CAAC,IAAI;MAAEC,IAAI,EAAE;IAAa,CAAC,EAClF;MAAEJ,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,WAAW,EAAE,QAAQ;MAAEC,MAAM,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAa,CAAC,CACnF,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,MAAM,CAAC;EAEpD,MAAM6B,oBAAoB,GAAGX,kBAAkB,CAACY,MAAM,CAACC,WAAW,IAAI;IACpE,MAAMC,aAAa,GAAGD,WAAW,CAACT,WAAW,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,UAAU,CAACQ,WAAW,CAAC,CAAC,CAAC,IACzEF,WAAW,CAACV,IAAI,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,UAAU,CAACQ,WAAW,CAAC,CAAC,CAAC;IACtF,MAAME,aAAa,GAAGR,UAAU,KAAK,MAAM,IAAII,WAAW,CAACV,IAAI,KAAKM,UAAU;IAC9E,OAAOK,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;EAEF,oBACE7B,OAAA;IAAK8B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB/B,OAAA;MAAK8B,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB/B,OAAA;QAAK8B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/B,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACrDlC,cAAc,CAACO,KAAK,CAACE,WAAW;QAAC;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACNrC,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAgB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eAENrC,OAAA;QAAK8B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/B,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACrDlC,cAAc,CAACO,KAAK,CAACG,aAAa;QAAC;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACNrC,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAgB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eAENrC,OAAA;QAAK8B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/B,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACrDlC,cAAc,CAACO,KAAK,CAACI,SAAS;QAAC;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACNrC,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eAENrC,OAAA;QAAK8B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/B,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACrDlC,cAAc,CAACO,KAAK,CAACK,kBAAkB;QAAC;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACNrC,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eAENrC,OAAA;QAAK8B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/B,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACrDlC,cAAc,CAACO,KAAK,CAACM,cAAc;QAAC;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACNrC,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eAENrC,OAAA;QAAK8B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/B,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACrDlC,cAAc,CAACO,KAAK,CAACO,eAAe;QAAC;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACNrC,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAK8B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB/B,OAAA;QAAK8B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B/B,OAAA;UAAI8B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7CrC,OAAA;UAAQ8B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAQ;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAENrC,OAAA;QAAK8B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B/B,OAAA;UAAO8B,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtB/B,OAAA;YAAA+B,QAAA,eACE/B,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAA+B,QAAA,EAAI;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdrC,OAAA;gBAAA+B,QAAA,EAAI;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdrC,OAAA;gBAAA+B,QAAA,EAAI;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfrC,OAAA;gBAAA+B,QAAA,EAAI;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRrC,OAAA;YAAA+B,QAAA,EACGnB,kBAAkB,CAAC0B,GAAG,CAAEb,WAAW,iBAClCzB,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAA+B,QAAA,eACE/B,OAAA;kBAAM8B,SAAS,EAAE,SAASL,WAAW,CAACV,IAAI,KAAK,OAAO,GAAG,eAAe,GAC9CU,WAAW,CAACV,IAAI,KAAK,OAAO,GAAG,cAAc,GAAG,eAAe,EAAG;kBAAAgB,QAAA,EACzFN,WAAW,CAACV;gBAAI;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLrC,OAAA;gBAAA+B,QAAA,EAAKN,WAAW,CAACT;cAAW;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClCrC,OAAA;gBAAIgC,KAAK,EAAE;kBACTC,KAAK,EAAER,WAAW,CAACR,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;kBACrDsB,UAAU,EAAE;gBACd,CAAE;gBAAAR,QAAA,EACClC,cAAc,CAAC2C,IAAI,CAACC,GAAG,CAAChB,WAAW,CAACR,MAAM,CAAC;cAAC;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACLrC,OAAA;gBAAA+B,QAAA,EAAKjC,UAAU,CAAC2B,WAAW,CAACP,IAAI;cAAC;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,GAdhCZ,WAAW,CAACX,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAenB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAK8B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB/B,OAAA;QAAK8B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B/B,OAAA;UAAI8B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/CrC,OAAA;UAAM8B,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eAENrC,OAAA;QAAK8B,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC/B,OAAA;UACE8B,SAAS,EAAC,kCAAkC;UAC5CY,OAAO,EAAEA,CAAA,KAAMvC,QAAQ,CAAC,qBAAqB,CAAE;UAAA4B,QAAA,gBAE/C/B,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrCrC,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/B,OAAA;cAAM8B,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDrC,OAAA;cAAM8B,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAwB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAETrC,OAAA;UACE8B,SAAS,EAAC,kCAAkC;UAC5CY,OAAO,EAAEA,CAAA,KAAMvC,QAAQ,CAAC,sBAAsB,CAAE;UAAA4B,QAAA,gBAEhD/B,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrCrC,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/B,OAAA;cAAM8B,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDrC,OAAA;cAAM8B,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAETrC,OAAA;UACE8B,SAAS,EAAC,kCAAkC;UAC5CY,OAAO,EAAEA,CAAA,KAAMvC,QAAQ,CAAC,oBAAoB,CAAE;UAAA4B,QAAA,gBAE9C/B,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrCrC,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/B,OAAA;cAAM8B,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjDrC,OAAA;cAAM8B,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAETrC,OAAA;UACE8B,SAAS,EAAC,oCAAoC;UAC9CY,OAAO,EAAEA,CAAA,KAAMvC,QAAQ,CAAC,UAAU,CAAE;UAAA4B,QAAA,gBAEpC/B,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrCrC,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/B,OAAA;cAAM8B,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDrC,OAAA;cAAM8B,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnC,EAAA,CA3LID,SAAS;EAAA,QACIL,WAAW;AAAA;AAAA+C,EAAA,GADxB1C,SAAS;AA6Lf,eAAeA,SAAS;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}