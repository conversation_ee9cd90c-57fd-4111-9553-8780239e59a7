{"ast": null, "code": "'use strict';\n\n/** @type {import('./functionCall')} */\nmodule.exports = Function.prototype.call;", "map": {"version": 3, "names": ["module", "exports", "Function", "prototype", "call"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/node_modules/call-bind-apply-helpers/functionCall.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./functionCall')} */\nmodule.exports = Function.prototype.call;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,QAAQ,CAACC,SAAS,CAACC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script"}