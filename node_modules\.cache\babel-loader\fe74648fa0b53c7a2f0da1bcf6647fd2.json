{"ast": null, "code": "'use strict';\n\nvar stringify = require('./stringify');\nvar parse = require('./parse');\nvar formats = require('./formats');\nmodule.exports = {\n  formats: formats,\n  parse: parse,\n  stringify: stringify\n};", "map": {"version": 3, "names": ["stringify", "require", "parse", "formats", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/node_modules/url/node_modules/qs/lib/index.js"], "sourcesContent": ["'use strict';\n\nvar stringify = require('./stringify');\nvar parse = require('./parse');\nvar formats = require('./formats');\n\nmodule.exports = {\n    formats: formats,\n    parse: parse,\n    stringify: stringify\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,SAAS,GAAGC,OAAO,CAAC,aAAa,CAAC;AACtC,IAAIC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;AAC9B,IAAIE,OAAO,GAAGF,OAAO,CAAC,WAAW,CAAC;AAElCG,MAAM,CAACC,OAAO,GAAG;EACbF,OAAO,EAAEA,OAAO;EAChBD,KAAK,EAAEA,KAAK;EACZF,SAAS,EAAEA;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}