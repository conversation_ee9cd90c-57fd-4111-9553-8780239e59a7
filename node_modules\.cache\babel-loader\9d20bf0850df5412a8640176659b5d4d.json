{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\Reports.js\";\nimport React, { useState } from 'react';\nimport { formatCurrency } from '../utils/currency';\nconst Reports = () => {\n  const [selectedMonth, setSelectedMonth] = useState(new Date().toISOString().slice(0, 7));\n  const [reportType, setReportType] = useState('شامل');\n  const reportData = {\n    income: 150000,\n    expenses: {\n      warehouse: 25000,\n      transport: 8500,\n      living: 5200,\n      paint: 12000,\n      factory: 18500,\n      employees: 15000\n    },\n    deposits: 67000,\n    withdrawals: 12000\n  };\n  const getTotalExpenses = () => {\n    return Object.values(reportData.expenses).reduce((sum, expense) => sum + expense, 0);\n  };\n  const getNetProfit = () => {\n    return reportData.income - getTotalExpenses();\n  };\n  const generatePDF = () => {\n    alert('سيتم إضافة وظيفة تصدير PDF قريباً');\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"reports\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    className: \"card-title\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 11\n    }\n  }, \"\\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: selectedMonth,\n    onChange: e => setSelectedMonth(e.target.value),\n    className: \"form-input\",\n    style: {\n      width: 'auto'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"2024-01\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 15\n    }\n  }, \"\\u064A\\u0646\\u0627\\u064A\\u0631 2024\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"2023-12\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 15\n    }\n  }, \"\\u062F\\u064A\\u0633\\u0645\\u0628\\u0631 2023\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"2023-11\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 15\n    }\n  }, \"\\u0646\\u0648\\u0641\\u0645\\u0628\\u0631 2023\")), /*#__PURE__*/React.createElement(\"select\", {\n    value: reportType,\n    onChange: e => setReportType(e.target.value),\n    className: \"form-input\",\n    style: {\n      width: 'auto'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\\u0634\\u0627\\u0645\\u0644\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 15\n    }\n  }, \"\\u062A\\u0642\\u0631\\u064A\\u0631 \\u0634\\u0627\\u0645\\u0644\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\\u0625\\u064A\\u0631\\u0627\\u062F\\u0627\\u062A\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 15\n    }\n  }, \"\\u0627\\u0644\\u0625\\u064A\\u0631\\u0627\\u062F\\u0627\\u062A \\u0641\\u0642\\u0637\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 15\n    }\n  }, \"\\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0641\\u0642\\u0637\")), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    onClick: generatePDF,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 13\n    }\n  }, \"\\uD83D\\uDCC4 \\u062A\\u0635\\u062F\\u064A\\u0631 PDF\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stats-grid\",\n    style: {\n      marginBottom: '30px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-value\",\n    style: {\n      color: '#ffa500'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 13\n    }\n  }, formatCurrency(reportData.income)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 13\n    }\n  }, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0625\\u064A\\u0631\\u0627\\u062F\\u0627\\u062A\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-value\",\n    style: {\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }\n  }, formatCurrency(getTotalExpenses())), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }\n  }, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-value\",\n    style: {\n      color: getNetProfit() >= 0 ? '#1e3a8a' : '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 13\n    }\n  }, formatCurrency(getNetProfit())), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 13\n    }\n  }, \"\\u0635\\u0627\\u0641\\u064A \\u0627\\u0644\\u0631\\u0628\\u062D/\\u0627\\u0644\\u062E\\u0633\\u0627\\u0631\\u0629\"))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'grid',\n      gridTemplateColumns: '1fr 1fr',\n      gap: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card\",\n    style: {\n      margin: 0\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    style: {\n      marginBottom: '15px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 13\n    }\n  }, \"\\u062A\\u0641\\u0635\\u064A\\u0644 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 21\n    }\n  }, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 21\n    }\n  }, \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 21\n    }\n  }, \"\\u0627\\u0644\\u0646\\u0633\\u0628\\u0629\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 21\n    }\n  }, \"\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0646\"), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 21\n    }\n  }, formatCurrency(reportData.expenses.warehouse)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 21\n    }\n  }, (reportData.expenses.warehouse / getTotalExpenses() * 100).toFixed(1), \"%\")), /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 21\n    }\n  }, \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0646\\u0642\\u0644\"), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 21\n    }\n  }, formatCurrency(reportData.expenses.transport)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 21\n    }\n  }, (reportData.expenses.transport / getTotalExpenses() * 100).toFixed(1), \"%\")), /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 21\n    }\n  }, \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0645\\u0639\\u064A\\u0634\\u0629\"), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 21\n    }\n  }, formatCurrency(reportData.expenses.living)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 21\n    }\n  }, (reportData.expenses.living / getTotalExpenses() * 100).toFixed(1), \"%\")), /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 21\n    }\n  }, \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0637\\u0644\\u0627\\u0621\"), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 21\n    }\n  }, formatCurrency(reportData.expenses.paint)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 21\n    }\n  }, (reportData.expenses.paint / getTotalExpenses() * 100).toFixed(1), \"%\")), /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 21\n    }\n  }, \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0645\\u0635\\u0646\\u0639\"), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 21\n    }\n  }, formatCurrency(reportData.expenses.factory)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 21\n    }\n  }, (reportData.expenses.factory / getTotalExpenses() * 100).toFixed(1), \"%\")), /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 21\n    }\n  }, \"\\u0645\\u0633\\u062D\\u0648\\u0628\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0638\\u0641\\u064A\\u0646\"), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 21\n    }\n  }, formatCurrency(reportData.expenses.employees)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 21\n    }\n  }, (reportData.expenses.employees / getTotalExpenses() * 100).toFixed(1), \"%\")))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card\",\n    style: {\n      margin: 0\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h4\", {\n    style: {\n      marginBottom: '15px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 13\n    }\n  }, \"\\u062D\\u0631\\u0643\\u0629 \\u0627\\u0644\\u062E\\u0632\\u064A\\u0646\\u0629\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      marginBottom: '15px',\n      display: 'flex',\n      justifyContent: 'space-between'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 17\n    }\n  }, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0625\\u064A\\u062F\\u0627\\u0639\\u0627\\u062A:\"), /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      color: '#ffa500'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 17\n    }\n  }, formatCurrency(reportData.deposits))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      marginBottom: '15px',\n      display: 'flex',\n      justifyContent: 'space-between'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 17\n    }\n  }, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0633\\u062D\\u0648\\u0628\\u0627\\u062A:\"), /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 17\n    }\n  }, formatCurrency(reportData.withdrawals))), /*#__PURE__*/React.createElement(\"hr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 15\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      fontSize: '18px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 17\n    }\n  }, \"\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u062E\\u0632\\u064A\\u0646\\u0629:\"), /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      color: '#1e3a8a'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 17\n    }\n  }, formatCurrency(reportData.deposits - reportData.withdrawals))))))));\n};\nexport default Reports;", "map": {"version": 3, "names": ["React", "useState", "formatCurrency", "Reports", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMonth", "Date", "toISOString", "slice", "reportType", "setReportType", "reportData", "income", "expenses", "warehouse", "transport", "living", "paint", "factory", "employees", "deposits", "withdrawals", "getTotalExpenses", "Object", "values", "reduce", "sum", "expense", "getNetProfit", "generatePDF", "alert", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "gap", "value", "onChange", "e", "target", "width", "onClick", "marginBottom", "color", "gridTemplateColumns", "margin", "toFixed", "padding", "justifyContent", "fontSize"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/Reports.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { formatCurrency } from '../utils/currency';\n\nconst Reports = () => {\n  const [selectedMonth, setSelectedMonth] = useState(new Date().toISOString().slice(0, 7));\n  const [reportType, setReportType] = useState('شامل');\n\n  const reportData = {\n    income: 150000,\n    expenses: {\n      warehouse: 25000,\n      transport: 8500,\n      living: 5200,\n      paint: 12000,\n      factory: 18500,\n      employees: 15000\n    },\n    deposits: 67000,\n    withdrawals: 12000\n  };\n\n\n\n  const getTotalExpenses = () => {\n    return Object.values(reportData.expenses).reduce((sum, expense) => sum + expense, 0);\n  };\n\n  const getNetProfit = () => {\n    return reportData.income - getTotalExpenses();\n  };\n\n  const generatePDF = () => {\n    alert('سيتم إضافة وظيفة تصدير PDF قريباً');\n  };\n\n  return (\n    <div className=\"reports\">\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">التقارير المالية</h3>\n          <div style={{ display: 'flex', gap: '10px' }}>\n            <select \n              value={selectedMonth}\n              onChange={(e) => setSelectedMonth(e.target.value)}\n              className=\"form-input\"\n              style={{ width: 'auto' }}\n            >\n              <option value=\"2024-01\">يناير 2024</option>\n              <option value=\"2023-12\">ديسمبر 2023</option>\n              <option value=\"2023-11\">نوفمبر 2023</option>\n            </select>\n            \n            <select \n              value={reportType}\n              onChange={(e) => setReportType(e.target.value)}\n              className=\"form-input\"\n              style={{ width: 'auto' }}\n            >\n              <option value=\"شامل\">تقرير شامل</option>\n              <option value=\"إيرادات\">الإيرادات فقط</option>\n              <option value=\"مصروفات\">المصروفات فقط</option>\n            </select>\n            \n            <button className=\"btn btn-primary\" onClick={generatePDF}>\n              📄 تصدير PDF\n            </button>\n          </div>\n        </div>\n\n        {/* ملخص مالي */}\n        <div className=\"stats-grid\" style={{ marginBottom: '30px' }}>\n          <div className=\"stat-card\">\n            <div className=\"stat-value\" style={{ color: '#ffa500' }}>\n              {formatCurrency(reportData.income)}\n            </div>\n            <div className=\"stat-label\">إجمالي الإيرادات</div>\n          </div>\n\n          <div className=\"stat-card\">\n            <div className=\"stat-value\" style={{ color: '#dc3545' }}>\n              {formatCurrency(getTotalExpenses())}\n            </div>\n            <div className=\"stat-label\">إجمالي المصروفات</div>\n          </div>\n\n          <div className=\"stat-card\">\n            <div className=\"stat-value\" style={{ color: getNetProfit() >= 0 ? '#1e3a8a' : '#dc3545' }}>\n              {formatCurrency(getNetProfit())}\n            </div>\n            <div className=\"stat-label\">صافي الربح/الخسارة</div>\n          </div>\n        </div>\n\n        {/* تفصيل المصروفات */}\n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>\n          <div className=\"card\" style={{ margin: 0 }}>\n            <h4 style={{ marginBottom: '15px' }}>تفصيل المصروفات</h4>\n            <div className=\"table-container\">\n              <table className=\"table\">\n                <thead>\n                  <tr>\n                    <th>نوع المصروف</th>\n                    <th>المبلغ</th>\n                    <th>النسبة</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr>\n                    <td>مشتريات المخزن</td>\n                    <td>{formatCurrency(reportData.expenses.warehouse)}</td>\n                    <td>{((reportData.expenses.warehouse / getTotalExpenses()) * 100).toFixed(1)}%</td>\n                  </tr>\n                  <tr>\n                    <td>مصروفات النقل</td>\n                    <td>{formatCurrency(reportData.expenses.transport)}</td>\n                    <td>{((reportData.expenses.transport / getTotalExpenses()) * 100).toFixed(1)}%</td>\n                  </tr>\n                  <tr>\n                    <td>مصروفات المعيشة</td>\n                    <td>{formatCurrency(reportData.expenses.living)}</td>\n                    <td>{((reportData.expenses.living / getTotalExpenses()) * 100).toFixed(1)}%</td>\n                  </tr>\n                  <tr>\n                    <td>مصروفات الطلاء</td>\n                    <td>{formatCurrency(reportData.expenses.paint)}</td>\n                    <td>{((reportData.expenses.paint / getTotalExpenses()) * 100).toFixed(1)}%</td>\n                  </tr>\n                  <tr>\n                    <td>مصروفات المصنع</td>\n                    <td>{formatCurrency(reportData.expenses.factory)}</td>\n                    <td>{((reportData.expenses.factory / getTotalExpenses()) * 100).toFixed(1)}%</td>\n                  </tr>\n                  <tr>\n                    <td>مسحوبات الموظفين</td>\n                    <td>{formatCurrency(reportData.expenses.employees)}</td>\n                    <td>{((reportData.expenses.employees / getTotalExpenses()) * 100).toFixed(1)}%</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          <div className=\"card\" style={{ margin: 0 }}>\n            <h4 style={{ marginBottom: '15px' }}>حركة الخزينة</h4>\n            <div style={{ padding: '20px' }}>\n              <div style={{ marginBottom: '15px', display: 'flex', justifyContent: 'space-between' }}>\n                <span>إجمالي الإيداعات:</span>\n                <strong style={{ color: '#ffa500' }}>{formatCurrency(reportData.deposits)}</strong>\n              </div>\n              <div style={{ marginBottom: '15px', display: 'flex', justifyContent: 'space-between' }}>\n                <span>إجمالي المسحوبات:</span>\n                <strong style={{ color: '#dc3545' }}>{formatCurrency(reportData.withdrawals)}</strong>\n              </div>\n              <hr />\n              <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '18px' }}>\n                <span>رصيد الخزينة:</span>\n                <strong style={{ color: '#1e3a8a' }}>\n                  {formatCurrency(reportData.deposits - reportData.withdrawals)}\n                </strong>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Reports;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,cAAc,QAAQ,mBAAmB;AAElD,MAAMC,OAAO,GAAGA,CAAA,KAAM;EACpB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGJ,QAAQ,CAAC,IAAIK,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,MAAM,CAAC;EAEpD,MAAMU,UAAU,GAAG;IACjBC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE;MACRC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,IAAI;MACfC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE;IACb,CAAC;IACDC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE;EACf,CAAC;EAID,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAOC,MAAM,CAACC,MAAM,CAACb,UAAU,CAACE,QAAQ,CAAC,CAACY,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,EAAE,CAAC,CAAC;EACtF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAOjB,UAAU,CAACC,MAAM,GAAGU,gBAAgB,CAAC,CAAC;EAC/C,CAAC;EAED,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACxBC,KAAK,CAAC,mCAAmC,CAAC;EAC5C,CAAC;EAED,oBACE9B,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBtC,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBtC,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BtC,KAAA,CAAA+B,aAAA;IAAIC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6FAAoB,CAAC,eAChDtC,KAAA,CAAA+B,aAAA;IAAKQ,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,GAAG,EAAE;IAAO,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3CtC,KAAA,CAAA+B,aAAA;IACEW,KAAK,EAAEtC,aAAc;IACrBuC,QAAQ,EAAGC,CAAC,IAAKvC,gBAAgB,CAACuC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;IAClDV,SAAS,EAAC,YAAY;IACtBO,KAAK,EAAE;MAAEO,KAAK,EAAE;IAAO,CAAE;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzBtC,KAAA,CAAA+B,aAAA;IAAQW,KAAK,EAAC,SAAS;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,qCAAkB,CAAC,eAC3CtC,KAAA,CAAA+B,aAAA;IAAQW,KAAK,EAAC,SAAS;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2CAAmB,CAAC,eAC5CtC,KAAA,CAAA+B,aAAA;IAAQW,KAAK,EAAC,SAAS;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2CAAmB,CACrC,CAAC,eAETtC,KAAA,CAAA+B,aAAA;IACEW,KAAK,EAAEjC,UAAW;IAClBkC,QAAQ,EAAGC,CAAC,IAAKlC,aAAa,CAACkC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;IAC/CV,SAAS,EAAC,YAAY;IACtBO,KAAK,EAAE;MAAEO,KAAK,EAAE;IAAO,CAAE;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzBtC,KAAA,CAAA+B,aAAA;IAAQW,KAAK,EAAC,0BAAM;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yDAAkB,CAAC,eACxCtC,KAAA,CAAA+B,aAAA;IAAQW,KAAK,EAAC,4CAAS;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2EAAqB,CAAC,eAC9CtC,KAAA,CAAA+B,aAAA;IAAQW,KAAK,EAAC,4CAAS;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2EAAqB,CACvC,CAAC,eAETtC,KAAA,CAAA+B,aAAA;IAAQC,SAAS,EAAC,iBAAiB;IAACe,OAAO,EAAElB,WAAY;IAAAI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,iDAElD,CACL,CACF,CAAC,eAGNtC,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAES,YAAY,EAAE;IAAO,CAAE;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1DtC,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBtC,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEU,KAAK,EAAE;IAAU,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACrDpC,cAAc,CAACS,UAAU,CAACC,MAAM,CAC9B,CAAC,eACNZ,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6FAAqB,CAC9C,CAAC,eAENtC,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBtC,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEU,KAAK,EAAE;IAAU,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACrDpC,cAAc,CAACoB,gBAAgB,CAAC,CAAC,CAC/B,CAAC,eACNtB,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6FAAqB,CAC9C,CAAC,eAENtC,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBtC,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEU,KAAK,EAAErB,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG;IAAU,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvFpC,cAAc,CAAC0B,YAAY,CAAC,CAAC,CAC3B,CAAC,eACN5B,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oGAAuB,CAChD,CACF,CAAC,eAGNtC,KAAA,CAAA+B,aAAA;IAAKQ,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEU,mBAAmB,EAAE,SAAS;MAAET,GAAG,EAAE;IAAO,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3EtC,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,MAAM;IAACO,KAAK,EAAE;MAAEY,MAAM,EAAE;IAAE,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzCtC,KAAA,CAAA+B,aAAA;IAAIQ,KAAK,EAAE;MAAES,YAAY,EAAE;IAAO,CAAE;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uFAAmB,CAAC,eACzDtC,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BtC,KAAA,CAAA+B,aAAA;IAAOC,SAAS,EAAC,OAAO;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,+DAAe,CAAC,eACpBtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sCAAU,CAAC,eACftC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sCAAU,CACZ,CACC,CAAC,eACRtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iFAAkB,CAAC,eACvBtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKpC,cAAc,CAACS,UAAU,CAACE,QAAQ,CAACC,SAAS,CAAM,CAAC,eACxDd,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,CAAE3B,UAAU,CAACE,QAAQ,CAACC,SAAS,GAAGQ,gBAAgB,CAAC,CAAC,GAAI,GAAG,EAAE8B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAK,CAChF,CAAC,eACLpD,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,2EAAiB,CAAC,eACtBtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKpC,cAAc,CAACS,UAAU,CAACE,QAAQ,CAACE,SAAS,CAAM,CAAC,eACxDf,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,CAAE3B,UAAU,CAACE,QAAQ,CAACE,SAAS,GAAGO,gBAAgB,CAAC,CAAC,GAAI,GAAG,EAAE8B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAK,CAChF,CAAC,eACLpD,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,uFAAmB,CAAC,eACxBtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKpC,cAAc,CAACS,UAAU,CAACE,QAAQ,CAACG,MAAM,CAAM,CAAC,eACrDhB,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,CAAE3B,UAAU,CAACE,QAAQ,CAACG,MAAM,GAAGM,gBAAgB,CAAC,CAAC,GAAI,GAAG,EAAE8B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAK,CAC7E,CAAC,eACLpD,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iFAAkB,CAAC,eACvBtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKpC,cAAc,CAACS,UAAU,CAACE,QAAQ,CAACI,KAAK,CAAM,CAAC,eACpDjB,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,CAAE3B,UAAU,CAACE,QAAQ,CAACI,KAAK,GAAGK,gBAAgB,CAAC,CAAC,GAAI,GAAG,EAAE8B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAK,CAC5E,CAAC,eACLpD,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iFAAkB,CAAC,eACvBtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKpC,cAAc,CAACS,UAAU,CAACE,QAAQ,CAACK,OAAO,CAAM,CAAC,eACtDlB,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,CAAE3B,UAAU,CAACE,QAAQ,CAACK,OAAO,GAAGI,gBAAgB,CAAC,CAAC,GAAI,GAAG,EAAE8B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAK,CAC9E,CAAC,eACLpD,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,6FAAoB,CAAC,eACzBtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKpC,cAAc,CAACS,UAAU,CAACE,QAAQ,CAACM,SAAS,CAAM,CAAC,eACxDnB,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,CAAE3B,UAAU,CAACE,QAAQ,CAACM,SAAS,GAAGG,gBAAgB,CAAC,CAAC,GAAI,GAAG,EAAE8B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAK,CAChF,CACC,CACF,CACJ,CACF,CAAC,eAENpD,KAAA,CAAA+B,aAAA;IAAKC,SAAS,EAAC,MAAM;IAACO,KAAK,EAAE;MAAEY,MAAM,EAAE;IAAE,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzCtC,KAAA,CAAA+B,aAAA;IAAIQ,KAAK,EAAE;MAAES,YAAY,EAAE;IAAO,CAAE;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,qEAAgB,CAAC,eACtDtC,KAAA,CAAA+B,aAAA;IAAKQ,KAAK,EAAE;MAAEc,OAAO,EAAE;IAAO,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BtC,KAAA,CAAA+B,aAAA;IAAKQ,KAAK,EAAE;MAAES,YAAY,EAAE,MAAM;MAAER,OAAO,EAAE,MAAM;MAAEc,cAAc,EAAE;IAAgB,CAAE;IAAArB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrFtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,8FAAuB,CAAC,eAC9BtC,KAAA,CAAA+B,aAAA;IAAQQ,KAAK,EAAE;MAAEU,KAAK,EAAE;IAAU,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEpC,cAAc,CAACS,UAAU,CAACS,QAAQ,CAAU,CAC/E,CAAC,eACNpB,KAAA,CAAA+B,aAAA;IAAKQ,KAAK,EAAE;MAAES,YAAY,EAAE,MAAM;MAAER,OAAO,EAAE,MAAM;MAAEc,cAAc,EAAE;IAAgB,CAAE;IAAArB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrFtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,8FAAuB,CAAC,eAC9BtC,KAAA,CAAA+B,aAAA;IAAQQ,KAAK,EAAE;MAAEU,KAAK,EAAE;IAAU,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEpC,cAAc,CAACS,UAAU,CAACU,WAAW,CAAU,CAClF,CAAC,eACNrB,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,eACNtC,KAAA,CAAA+B,aAAA;IAAKQ,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEc,cAAc,EAAE,eAAe;MAAEC,QAAQ,EAAE;IAAO,CAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjFtC,KAAA,CAAA+B,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,sEAAmB,CAAC,eAC1BtC,KAAA,CAAA+B,aAAA;IAAQQ,KAAK,EAAE;MAAEU,KAAK,EAAE;IAAU,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjCpC,cAAc,CAACS,UAAU,CAACS,QAAQ,GAAGT,UAAU,CAACU,WAAW,CACtD,CACL,CACF,CACF,CACF,CACF,CACF,CAAC;AAEV,CAAC;AAED,eAAelB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}