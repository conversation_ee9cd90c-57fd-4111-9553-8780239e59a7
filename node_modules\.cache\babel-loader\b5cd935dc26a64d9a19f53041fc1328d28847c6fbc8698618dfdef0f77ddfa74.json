{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\TransportExpenses.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { formatCurrency, formatDate } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TransportExpenses = () => {\n  _s();\n  const [expenses, setExpenses] = useState([{\n    id: 1,\n    date: '2024-01-15',\n    transportType: 'شحن بضائع',\n    destination: 'طرابلس',\n    amount: 3200,\n    driverName: 'أحمد محمد علي',\n    vehicleNumber: 'ط ر ب 1234',\n    notes: 'شحن سريع - بضائع حساسة',\n    distance: 450,\n    fuelCost: 800,\n    driverFee: 1200,\n    otherExpenses: 1200\n  }, {\n    id: 2,\n    date: '2024-01-14',\n    transportType: 'نقل موظفين',\n    destination: 'بنغازي',\n    amount: 2800,\n    driverName: 'محمد أحمد سالم',\n    vehicleNumber: 'ب ن غ 5678',\n    notes: 'نقل فريق الصيانة',\n    distance: 650,\n    fuelCost: 1100,\n    driverFee: 1000,\n    otherExpenses: 700\n  }, {\n    id: 3,\n    date: '2024-01-13',\n    transportType: 'توصيل مواد',\n    destination: 'مصراتة',\n    amount: 1800,\n    driverName: 'سالم عبدالله',\n    vehicleNumber: 'م ص ر 9012',\n    notes: 'توصيل مواد خام',\n    distance: 200,\n    fuelCost: 400,\n    driverFee: 800,\n    otherExpenses: 600\n  }, {\n    id: 4,\n    date: '2024-01-12',\n    transportType: 'شحن معدات',\n    destination: 'سبها',\n    amount: 4500,\n    driverName: 'عبدالرحمن محمد',\n    vehicleNumber: 'س ب ه 3456',\n    notes: 'شحن معدات ثقيلة',\n    distance: 750,\n    fuelCost: 1500,\n    driverFee: 1800,\n    otherExpenses: 1200\n  }]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    transportType: '',\n    destination: '',\n    amount: '',\n    driverName: '',\n    vehicleNumber: '',\n    notes: '',\n    distance: '',\n    fuelCost: '',\n    driverFee: '',\n    otherExpenses: ''\n  });\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    const newExpense = {\n      id: expenses.length + 1,\n      ...formData,\n      amount: parseFloat(formData.amount) || 0,\n      distance: parseInt(formData.distance) || 0,\n      fuelCost: parseFloat(formData.fuelCost) || 0,\n      driverFee: parseFloat(formData.driverFee) || 0,\n      otherExpenses: parseFloat(formData.otherExpenses) || 0\n    };\n    setExpenses(prev => [newExpense, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      transportType: '',\n      destination: '',\n      amount: '',\n      driverName: '',\n      vehicleNumber: '',\n      notes: '',\n      distance: '',\n      fuelCost: '',\n      driverFee: '',\n      otherExpenses: ''\n    });\n    setShowForm(false);\n  };\n  const getTotalExpenses = () => {\n    return expenses.reduce((sum, expense) => sum + expense.amount, 0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"transport-expenses\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#dc3545'\n          },\n          children: formatCurrency(getTotalExpenses())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0646\\u0642\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#1e3a8a'\n          },\n          children: expenses.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0631\\u062D\\u0644\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#ffa500'\n          },\n          children: [expenses.reduce((sum, expense) => sum + expense.distance, 0), \" \\u0643\\u0645\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0633\\u0627\\u0641\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#ea580c'\n          },\n          children: formatCurrency(expenses.reduce((sum, expense) => sum + expense.fuelCost, 0))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u062A\\u0643\\u0644\\u0641\\u0629 \\u0627\\u0644\\u0648\\u0642\\u0648\\u062F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u062D\\u0631\\u0643\\u0629 \\u0648\\u0627\\u0644\\u0646\\u0642\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => setShowForm(!showForm),\n          children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0635\\u0631\\u0648\\u0641 \\u0646\\u0642\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0646\\u0642\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0648\\u062C\\u0647\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0645\\u0633\\u0627\\u0641\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u062A\\u0643\\u0644\\u0641\\u0629 \\u0627\\u0644\\u0648\\u0642\\u0648\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0623\\u062C\\u0631\\u0629 \\u0627\\u0644\\u0633\\u0627\\u0626\\u0642\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0633\\u0627\\u0626\\u0642\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0645\\u0631\\u0643\\u0628\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: expenses.map(expense => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatDate(expense.date)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.transportType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.destination\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [expense.distance, \" \\u0643\\u0645\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  color: '#ea580c'\n                },\n                children: formatCurrency(expense.fuelCost)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  color: '#1e40af'\n                },\n                children: formatCurrency(expense.driverFee)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  fontWeight: 'bold',\n                  color: '#dc3545'\n                },\n                children: formatCurrency(expense.amount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.driverName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.vehicleNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '5px 10px',\n                    fontSize: '12px'\n                  },\n                  children: \"\\u270F\\uFE0F \\u062A\\u0639\\u062F\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)]\n            }, expense.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n};\n_s(TransportExpenses, \"nz+g16R/NgNSC+p9eYngfcUjXNk=\");\n_c = TransportExpenses;\nexport default TransportExpenses;\nvar _c;\n$RefreshReg$(_c, \"TransportExpenses\");", "map": {"version": 3, "names": ["React", "useState", "formatCurrency", "formatDate", "jsxDEV", "_jsxDEV", "TransportExpenses", "_s", "expenses", "setExpenses", "id", "date", "transportType", "destination", "amount", "<PERSON><PERSON><PERSON>", "vehicleNumber", "notes", "distance", "fuelCost", "driver<PERSON>ee", "otherExpenses", "showForm", "setShowForm", "formData", "setFormData", "Date", "toISOString", "split", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "newExpense", "length", "parseFloat", "parseInt", "getTotalExpenses", "reduce", "sum", "expense", "className", "children", "style", "marginBottom", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "fontWeight", "padding", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/TransportExpenses.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { formatCurrency, formatDate } from '../utils/currency';\n\nconst TransportExpenses = () => {\n  const [expenses, setExpenses] = useState([\n    {\n      id: 1,\n      date: '2024-01-15',\n      transportType: 'شحن بضائع',\n      destination: 'طرابلس',\n      amount: 3200,\n      driverName: 'أحمد محمد علي',\n      vehicleNumber: 'ط ر ب 1234',\n      notes: 'شحن سريع - بضائع حساسة',\n      distance: 450,\n      fuelCost: 800,\n      driverFee: 1200,\n      otherExpenses: 1200\n    },\n    {\n      id: 2,\n      date: '2024-01-14',\n      transportType: 'نقل موظفين',\n      destination: 'بنغازي',\n      amount: 2800,\n      driverName: 'محمد أحمد سالم',\n      vehicleNumber: 'ب ن غ 5678',\n      notes: 'نقل فريق الصيانة',\n      distance: 650,\n      fuelCost: 1100,\n      driverFee: 1000,\n      otherExpenses: 700\n    },\n    {\n      id: 3,\n      date: '2024-01-13',\n      transportType: 'توصيل مواد',\n      destination: 'مصراتة',\n      amount: 1800,\n      driverName: 'سالم عبدالله',\n      vehicleNumber: 'م ص ر 9012',\n      notes: 'توصيل مواد خام',\n      distance: 200,\n      fuelCost: 400,\n      driverFee: 800,\n      otherExpenses: 600\n    },\n    {\n      id: 4,\n      date: '2024-01-12',\n      transportType: 'شحن معدات',\n      destination: 'سبها',\n      amount: 4500,\n      driverName: 'عبدالرحمن محمد',\n      vehicleNumber: 'س ب ه 3456',\n      notes: 'شحن معدات ثقيلة',\n      distance: 750,\n      fuelCost: 1500,\n      driverFee: 1800,\n      otherExpenses: 1200\n    }\n  ]);\n\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    transportType: '',\n    destination: '',\n    amount: '',\n    driverName: '',\n    vehicleNumber: '',\n    notes: '',\n    distance: '',\n    fuelCost: '',\n    driverFee: '',\n    otherExpenses: ''\n  });\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n\n    const newExpense = {\n      id: expenses.length + 1,\n      ...formData,\n      amount: parseFloat(formData.amount) || 0,\n      distance: parseInt(formData.distance) || 0,\n      fuelCost: parseFloat(formData.fuelCost) || 0,\n      driverFee: parseFloat(formData.driverFee) || 0,\n      otherExpenses: parseFloat(formData.otherExpenses) || 0\n    };\n\n    setExpenses(prev => [newExpense, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      transportType: '',\n      destination: '',\n      amount: '',\n      driverName: '',\n      vehicleNumber: '',\n      notes: '',\n      distance: '',\n      fuelCost: '',\n      driverFee: '',\n      otherExpenses: ''\n    });\n    setShowForm(false);\n  };\n\n  const getTotalExpenses = () => {\n    return expenses.reduce((sum, expense) => sum + expense.amount, 0);\n  };\n\n  return (\n    <div className=\"transport-expenses\">\n      <div className=\"stats-grid\" style={{ marginBottom: '30px' }}>\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#dc3545' }}>\n            {formatCurrency(getTotalExpenses())}\n          </div>\n          <div className=\"stat-label\">إجمالي مصروفات النقل</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#1e3a8a' }}>\n            {expenses.length}\n          </div>\n          <div className=\"stat-label\">عدد الرحلات</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#ffa500' }}>\n            {expenses.reduce((sum, expense) => sum + expense.distance, 0)} كم\n          </div>\n          <div className=\"stat-label\">إجمالي المسافة</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#ea580c' }}>\n            {formatCurrency(expenses.reduce((sum, expense) => sum + expense.fuelCost, 0))}\n          </div>\n          <div className=\"stat-label\">تكلفة الوقود</div>\n        </div>\n      </div>\n\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">مصروفات الحركة والنقل</h3>\n          <button \n            className=\"btn btn-primary\"\n            onClick={() => setShowForm(!showForm)}\n          >\n            ➕ إضافة مصروف نقل\n          </button>\n        </div>\n\n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>التاريخ</th>\n                <th>نوع النقل</th>\n                <th>الوجهة</th>\n                <th>المسافة</th>\n                <th>تكلفة الوقود</th>\n                <th>أجرة السائق</th>\n                <th>إجمالي المبلغ</th>\n                <th>اسم السائق</th>\n                <th>رقم المركبة</th>\n                <th>إجراءات</th>\n              </tr>\n            </thead>\n            <tbody>\n              {expenses.map((expense) => (\n                <tr key={expense.id}>\n                  <td>{formatDate(expense.date)}</td>\n                  <td>{expense.transportType}</td>\n                  <td>{expense.destination}</td>\n                  <td>{expense.distance} كم</td>\n                  <td style={{ color: '#ea580c' }}>{formatCurrency(expense.fuelCost)}</td>\n                  <td style={{ color: '#1e40af' }}>{formatCurrency(expense.driverFee)}</td>\n                  <td style={{ fontWeight: 'bold', color: '#dc3545' }}>\n                    {formatCurrency(expense.amount)}\n                  </td>\n                  <td>{expense.driverName}</td>\n                  <td>{expense.vehicleNumber}</td>\n                  <td>\n                    <button className=\"btn btn-secondary\" style={{ padding: '5px 10px', fontSize: '12px' }}>\n                      ✏️ تعديل\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TransportExpenses;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,cAAc,EAAEC,UAAU,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,CACvC;IACES,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,aAAa,EAAE,WAAW;IAC1BC,WAAW,EAAE,QAAQ;IACrBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,eAAe;IAC3BC,aAAa,EAAE,YAAY;IAC3BC,KAAK,EAAE,wBAAwB;IAC/BC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE;EACjB,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,aAAa,EAAE,YAAY;IAC3BC,WAAW,EAAE,QAAQ;IACrBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,gBAAgB;IAC5BC,aAAa,EAAE,YAAY;IAC3BC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE;EACjB,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,aAAa,EAAE,YAAY;IAC3BC,WAAW,EAAE,QAAQ;IACrBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,cAAc;IAC1BC,aAAa,EAAE,YAAY;IAC3BC,KAAK,EAAE,gBAAgB;IACvBC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,GAAG;IACdC,aAAa,EAAE;EACjB,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,aAAa,EAAE,WAAW;IAC1BC,WAAW,EAAE,MAAM;IACnBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,gBAAgB;IAC5BC,aAAa,EAAE,YAAY;IAC3BC,KAAK,EAAE,iBAAiB;IACxBC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE;EACjB,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC;IACvCU,IAAI,EAAE,IAAIe,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5ChB,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAMQ,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCR,WAAW,CAACS,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAIL,CAAC,IAAK;IAC1BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,MAAMC,UAAU,GAAG;MACjB3B,EAAE,EAAEF,QAAQ,CAAC8B,MAAM,GAAG,CAAC;MACvB,GAAGd,QAAQ;MACXV,MAAM,EAAEyB,UAAU,CAACf,QAAQ,CAACV,MAAM,CAAC,IAAI,CAAC;MACxCI,QAAQ,EAAEsB,QAAQ,CAAChB,QAAQ,CAACN,QAAQ,CAAC,IAAI,CAAC;MAC1CC,QAAQ,EAAEoB,UAAU,CAACf,QAAQ,CAACL,QAAQ,CAAC,IAAI,CAAC;MAC5CC,SAAS,EAAEmB,UAAU,CAACf,QAAQ,CAACJ,SAAS,CAAC,IAAI,CAAC;MAC9CC,aAAa,EAAEkB,UAAU,CAACf,QAAQ,CAACH,aAAa,CAAC,IAAI;IACvD,CAAC;IAEDZ,WAAW,CAACyB,IAAI,IAAI,CAACG,UAAU,EAAE,GAAGH,IAAI,CAAC,CAAC;IAC1CT,WAAW,CAAC;MACVd,IAAI,EAAE,IAAIe,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5ChB,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE,EAAE;MACdC,aAAa,EAAE,EAAE;MACjBC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,aAAa,EAAE;IACjB,CAAC,CAAC;IACFE,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMkB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAOjC,QAAQ,CAACkC,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,CAAC9B,MAAM,EAAE,CAAC,CAAC;EACnE,CAAC;EAED,oBACET,OAAA;IAAKwC,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjCzC,OAAA;MAAKwC,SAAS,EAAC,YAAY;MAACE,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBAC1DzC,OAAA;QAAKwC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzC,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrD5C,cAAc,CAACuC,gBAAgB,CAAC,CAAC;QAAC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACNhD,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAoB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAENhD,OAAA;QAAKwC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzC,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrDtC,QAAQ,CAAC8B;QAAM;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACNhD,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAW;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eAENhD,OAAA;QAAKwC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzC,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,GACrDtC,QAAQ,CAACkC,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,CAAC1B,QAAQ,EAAE,CAAC,CAAC,EAAC,eAChE;QAAA;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhD,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAENhD,OAAA;QAAKwC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzC,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAU,CAAE;UAAAH,QAAA,EACrD5C,cAAc,CAACM,QAAQ,CAACkC,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,CAACzB,QAAQ,EAAE,CAAC,CAAC;QAAC;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eACNhD,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAY;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhD,OAAA;MAAKwC,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBzC,OAAA;QAAKwC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzC,OAAA;UAAIwC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAqB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrDhD,OAAA;UACEwC,SAAS,EAAC,iBAAiB;UAC3BS,OAAO,EAAEA,CAAA,KAAM/B,WAAW,CAAC,CAACD,QAAQ,CAAE;UAAAwB,QAAA,EACvC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENhD,OAAA;QAAKwC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BzC,OAAA;UAAOwC,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtBzC,OAAA;YAAAyC,QAAA,eACEzC,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAAyC,QAAA,EAAI;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBhD,OAAA;gBAAAyC,QAAA,EAAI;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBhD,OAAA;gBAAAyC,QAAA,EAAI;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfhD,OAAA;gBAAAyC,QAAA,EAAI;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBhD,OAAA;gBAAAyC,QAAA,EAAI;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBhD,OAAA;gBAAAyC,QAAA,EAAI;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBhD,OAAA;gBAAAyC,QAAA,EAAI;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBhD,OAAA;gBAAAyC,QAAA,EAAI;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBhD,OAAA;gBAAAyC,QAAA,EAAI;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBhD,OAAA;gBAAAyC,QAAA,EAAI;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRhD,OAAA;YAAAyC,QAAA,EACGtC,QAAQ,CAAC+C,GAAG,CAAEX,OAAO,iBACpBvC,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAAyC,QAAA,EAAK3C,UAAU,CAACyC,OAAO,CAACjC,IAAI;cAAC;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnChD,OAAA;gBAAAyC,QAAA,EAAKF,OAAO,CAAChC;cAAa;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChChD,OAAA;gBAAAyC,QAAA,EAAKF,OAAO,CAAC/B;cAAW;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9BhD,OAAA;gBAAAyC,QAAA,GAAKF,OAAO,CAAC1B,QAAQ,EAAC,eAAG;cAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9BhD,OAAA;gBAAI0C,KAAK,EAAE;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAH,QAAA,EAAE5C,cAAc,CAAC0C,OAAO,CAACzB,QAAQ;cAAC;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxEhD,OAAA;gBAAI0C,KAAK,EAAE;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAH,QAAA,EAAE5C,cAAc,CAAC0C,OAAO,CAACxB,SAAS;cAAC;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzEhD,OAAA;gBAAI0C,KAAK,EAAE;kBAAES,UAAU,EAAE,MAAM;kBAAEP,KAAK,EAAE;gBAAU,CAAE;gBAAAH,QAAA,EACjD5C,cAAc,CAAC0C,OAAO,CAAC9B,MAAM;cAAC;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACLhD,OAAA;gBAAAyC,QAAA,EAAKF,OAAO,CAAC7B;cAAU;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7BhD,OAAA;gBAAAyC,QAAA,EAAKF,OAAO,CAAC5B;cAAa;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChChD,OAAA;gBAAAyC,QAAA,eACEzC,OAAA;kBAAQwC,SAAS,EAAC,mBAAmB;kBAACE,KAAK,EAAE;oBAAEU,OAAO,EAAE,UAAU;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAAZ,QAAA,EAAC;gBAExF;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAhBET,OAAO,CAAClC,EAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBf,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9C,EAAA,CA3MID,iBAAiB;AAAAqD,EAAA,GAAjBrD,iBAAiB;AA6MvB,eAAeA,iBAAiB;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}