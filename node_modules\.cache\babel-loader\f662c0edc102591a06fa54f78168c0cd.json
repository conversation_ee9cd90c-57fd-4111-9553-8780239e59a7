{"ast": null, "code": "'use strict';\n\nconst TEMPLATE_REGEX = /(?:\\\\(u[a-f\\d]{4}|x[a-f\\d]{2}|.))|(?:\\{(~)?(\\w+(?:\\([^)]*\\))?(?:\\.\\w+(?:\\([^)]*\\))?)*)(?:[ \\t]|(?=\\r?\\n)))|(\\})|((?:.|[\\r\\n\\f])+?)/gi;\nconst STYLE_REGEX = /(?:^|\\.)(\\w+)(?:\\(([^)]*)\\))?/g;\nconst STRING_REGEX = /^(['\"])((?:\\\\.|(?!\\1)[^\\\\])*)\\1$/;\nconst ESCAPE_REGEX = /\\\\(u[a-f\\d]{4}|x[a-f\\d]{2}|.)|([^\\\\])/gi;\nconst ESCAPES = new Map([['n', '\\n'], ['r', '\\r'], ['t', '\\t'], ['b', '\\b'], ['f', '\\f'], ['v', '\\v'], ['0', '\\0'], ['\\\\', '\\\\'], ['e', '\\u001B'], ['a', '\\u0007']]);\nfunction unescape(c) {\n  if (c[0] === 'u' && c.length === 5 || c[0] === 'x' && c.length === 3) {\n    return String.fromCharCode(parseInt(c.slice(1), 16));\n  }\n  return ESCAPES.get(c) || c;\n}\nfunction parseArguments(name, args) {\n  const results = [];\n  const chunks = args.trim().split(/\\s*,\\s*/g);\n  let matches;\n  for (const chunk of chunks) {\n    if (!isNaN(chunk)) {\n      results.push(Number(chunk));\n    } else if (matches = chunk.match(STRING_REGEX)) {\n      results.push(matches[2].replace(ESCAPE_REGEX, (m, escape, chr) => escape ? unescape(escape) : chr));\n    } else {\n      throw new Error(`Invalid Chalk template style argument: ${chunk} (in style '${name}')`);\n    }\n  }\n  return results;\n}\nfunction parseStyle(style) {\n  STYLE_REGEX.lastIndex = 0;\n  const results = [];\n  let matches;\n  while ((matches = STYLE_REGEX.exec(style)) !== null) {\n    const name = matches[1];\n    if (matches[2]) {\n      const args = parseArguments(name, matches[2]);\n      results.push([name].concat(args));\n    } else {\n      results.push([name]);\n    }\n  }\n  return results;\n}\nfunction buildStyle(chalk, styles) {\n  const enabled = {};\n  for (const layer of styles) {\n    for (const style of layer.styles) {\n      enabled[style[0]] = layer.inverse ? null : style.slice(1);\n    }\n  }\n  let current = chalk;\n  for (const styleName of Object.keys(enabled)) {\n    if (Array.isArray(enabled[styleName])) {\n      if (!(styleName in current)) {\n        throw new Error(`Unknown Chalk style: ${styleName}`);\n      }\n      if (enabled[styleName].length > 0) {\n        current = current[styleName].apply(current, enabled[styleName]);\n      } else {\n        current = current[styleName];\n      }\n    }\n  }\n  return current;\n}\nmodule.exports = (chalk, tmp) => {\n  const styles = [];\n  const chunks = [];\n  let chunk = [];\n\n  // eslint-disable-next-line max-params\n  tmp.replace(TEMPLATE_REGEX, (m, escapeChar, inverse, style, close, chr) => {\n    if (escapeChar) {\n      chunk.push(unescape(escapeChar));\n    } else if (style) {\n      const str = chunk.join('');\n      chunk = [];\n      chunks.push(styles.length === 0 ? str : buildStyle(chalk, styles)(str));\n      styles.push({\n        inverse,\n        styles: parseStyle(style)\n      });\n    } else if (close) {\n      if (styles.length === 0) {\n        throw new Error('Found extraneous } in Chalk template literal');\n      }\n      chunks.push(buildStyle(chalk, styles)(chunk.join('')));\n      chunk = [];\n      styles.pop();\n    } else {\n      chunk.push(chr);\n    }\n  });\n  chunks.push(chunk.join(''));\n  if (styles.length > 0) {\n    const errMsg = `Chalk template literal is missing ${styles.length} closing bracket${styles.length === 1 ? '' : 's'} (\\`}\\`)`;\n    throw new Error(errMsg);\n  }\n  return chunks.join('');\n};", "map": {"version": 3, "names": ["TEMPLATE_REGEX", "STYLE_REGEX", "STRING_REGEX", "ESCAPE_REGEX", "ESCAPES", "Map", "unescape", "c", "length", "String", "fromCharCode", "parseInt", "slice", "get", "parseArguments", "name", "args", "results", "chunks", "trim", "split", "matches", "chunk", "isNaN", "push", "Number", "match", "replace", "m", "escape", "chr", "Error", "parseStyle", "style", "lastIndex", "exec", "concat", "buildStyle", "chalk", "styles", "enabled", "layer", "inverse", "current", "styleName", "Object", "keys", "Array", "isArray", "apply", "module", "exports", "tmp", "escapeChar", "close", "str", "join", "pop", "errMsg"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/node_modules/react-dev-utils/node_modules/chalk/templates.js"], "sourcesContent": ["'use strict';\nconst TEMPLATE_REGEX = /(?:\\\\(u[a-f\\d]{4}|x[a-f\\d]{2}|.))|(?:\\{(~)?(\\w+(?:\\([^)]*\\))?(?:\\.\\w+(?:\\([^)]*\\))?)*)(?:[ \\t]|(?=\\r?\\n)))|(\\})|((?:.|[\\r\\n\\f])+?)/gi;\nconst STYLE_REGEX = /(?:^|\\.)(\\w+)(?:\\(([^)]*)\\))?/g;\nconst STRING_REGEX = /^(['\"])((?:\\\\.|(?!\\1)[^\\\\])*)\\1$/;\nconst ESCAPE_REGEX = /\\\\(u[a-f\\d]{4}|x[a-f\\d]{2}|.)|([^\\\\])/gi;\n\nconst ESCAPES = new Map([\n\t['n', '\\n'],\n\t['r', '\\r'],\n\t['t', '\\t'],\n\t['b', '\\b'],\n\t['f', '\\f'],\n\t['v', '\\v'],\n\t['0', '\\0'],\n\t['\\\\', '\\\\'],\n\t['e', '\\u001B'],\n\t['a', '\\u0007']\n]);\n\nfunction unescape(c) {\n\tif ((c[0] === 'u' && c.length === 5) || (c[0] === 'x' && c.length === 3)) {\n\t\treturn String.fromCharCode(parseInt(c.slice(1), 16));\n\t}\n\n\treturn ESCAPES.get(c) || c;\n}\n\nfunction parseArguments(name, args) {\n\tconst results = [];\n\tconst chunks = args.trim().split(/\\s*,\\s*/g);\n\tlet matches;\n\n\tfor (const chunk of chunks) {\n\t\tif (!isNaN(chunk)) {\n\t\t\tresults.push(Number(chunk));\n\t\t} else if ((matches = chunk.match(STRING_REGEX))) {\n\t\t\tresults.push(matches[2].replace(ESCAPE_REGEX, (m, escape, chr) => escape ? unescape(escape) : chr));\n\t\t} else {\n\t\t\tthrow new Error(`Invalid Chalk template style argument: ${chunk} (in style '${name}')`);\n\t\t}\n\t}\n\n\treturn results;\n}\n\nfunction parseStyle(style) {\n\tSTYLE_REGEX.lastIndex = 0;\n\n\tconst results = [];\n\tlet matches;\n\n\twhile ((matches = STYLE_REGEX.exec(style)) !== null) {\n\t\tconst name = matches[1];\n\n\t\tif (matches[2]) {\n\t\t\tconst args = parseArguments(name, matches[2]);\n\t\t\tresults.push([name].concat(args));\n\t\t} else {\n\t\t\tresults.push([name]);\n\t\t}\n\t}\n\n\treturn results;\n}\n\nfunction buildStyle(chalk, styles) {\n\tconst enabled = {};\n\n\tfor (const layer of styles) {\n\t\tfor (const style of layer.styles) {\n\t\t\tenabled[style[0]] = layer.inverse ? null : style.slice(1);\n\t\t}\n\t}\n\n\tlet current = chalk;\n\tfor (const styleName of Object.keys(enabled)) {\n\t\tif (Array.isArray(enabled[styleName])) {\n\t\t\tif (!(styleName in current)) {\n\t\t\t\tthrow new Error(`Unknown Chalk style: ${styleName}`);\n\t\t\t}\n\n\t\t\tif (enabled[styleName].length > 0) {\n\t\t\t\tcurrent = current[styleName].apply(current, enabled[styleName]);\n\t\t\t} else {\n\t\t\t\tcurrent = current[styleName];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn current;\n}\n\nmodule.exports = (chalk, tmp) => {\n\tconst styles = [];\n\tconst chunks = [];\n\tlet chunk = [];\n\n\t// eslint-disable-next-line max-params\n\ttmp.replace(TEMPLATE_REGEX, (m, escapeChar, inverse, style, close, chr) => {\n\t\tif (escapeChar) {\n\t\t\tchunk.push(unescape(escapeChar));\n\t\t} else if (style) {\n\t\t\tconst str = chunk.join('');\n\t\t\tchunk = [];\n\t\t\tchunks.push(styles.length === 0 ? str : buildStyle(chalk, styles)(str));\n\t\t\tstyles.push({inverse, styles: parseStyle(style)});\n\t\t} else if (close) {\n\t\t\tif (styles.length === 0) {\n\t\t\t\tthrow new Error('Found extraneous } in Chalk template literal');\n\t\t\t}\n\n\t\t\tchunks.push(buildStyle(chalk, styles)(chunk.join('')));\n\t\t\tchunk = [];\n\t\t\tstyles.pop();\n\t\t} else {\n\t\t\tchunk.push(chr);\n\t\t}\n\t});\n\n\tchunks.push(chunk.join(''));\n\n\tif (styles.length > 0) {\n\t\tconst errMsg = `Chalk template literal is missing ${styles.length} closing bracket${styles.length === 1 ? '' : 's'} (\\`}\\`)`;\n\t\tthrow new Error(errMsg);\n\t}\n\n\treturn chunks.join('');\n};\n"], "mappings": "AAAA,YAAY;;AACZ,MAAMA,cAAc,GAAG,sIAAsI;AAC7J,MAAMC,WAAW,GAAG,gCAAgC;AACpD,MAAMC,YAAY,GAAG,kCAAkC;AACvD,MAAMC,YAAY,GAAG,y<PERSON>AyC;AAE9D,MAAMC,OAAO,GAAG,IAAIC,GAAG,CAAC,CACvB,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,IAAI,EAAE,IAAI,CAAC,EACZ,CAAC,GAAG,EAAE,QAAQ,CAAC,EACf,CAAC,GAAG,EAAE,QAAQ,CAAC,CACf,CAAC;AAEF,SAASC,QAAQA,CAACC,CAAC,EAAE;EACpB,IAAKA,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,CAAC,CAACC,MAAM,KAAK,CAAC,IAAMD,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,CAAC,CAACC,MAAM,KAAK,CAAE,EAAE;IACzE,OAAOC,MAAM,CAACC,YAAY,CAACC,QAAQ,CAACJ,CAAC,CAACK,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EACrD;EAEA,OAAOR,OAAO,CAACS,GAAG,CAACN,CAAC,CAAC,IAAIA,CAAC;AAC3B;AAEA,SAASO,cAAcA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACnC,MAAMC,OAAO,GAAG,EAAE;EAClB,MAAMC,MAAM,GAAGF,IAAI,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,UAAU,CAAC;EAC5C,IAAIC,OAAO;EAEX,KAAK,MAAMC,KAAK,IAAIJ,MAAM,EAAE;IAC3B,IAAI,CAACK,KAAK,CAACD,KAAK,CAAC,EAAE;MAClBL,OAAO,CAACO,IAAI,CAACC,MAAM,CAACH,KAAK,CAAC,CAAC;IAC5B,CAAC,MAAM,IAAKD,OAAO,GAAGC,KAAK,CAACI,KAAK,CAACxB,YAAY,CAAC,EAAG;MACjDe,OAAO,CAACO,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC,CAACM,OAAO,CAACxB,YAAY,EAAE,CAACyB,CAAC,EAAEC,MAAM,EAAEC,GAAG,KAAKD,MAAM,GAAGvB,QAAQ,CAACuB,MAAM,CAAC,GAAGC,GAAG,CAAC,CAAC;IACpG,CAAC,MAAM;MACN,MAAM,IAAIC,KAAK,CAAC,0CAA0CT,KAAK,eAAeP,IAAI,IAAI,CAAC;IACxF;EACD;EAEA,OAAOE,OAAO;AACf;AAEA,SAASe,UAAUA,CAACC,KAAK,EAAE;EAC1BhC,WAAW,CAACiC,SAAS,GAAG,CAAC;EAEzB,MAAMjB,OAAO,GAAG,EAAE;EAClB,IAAII,OAAO;EAEX,OAAO,CAACA,OAAO,GAAGpB,WAAW,CAACkC,IAAI,CAACF,KAAK,CAAC,MAAM,IAAI,EAAE;IACpD,MAAMlB,IAAI,GAAGM,OAAO,CAAC,CAAC,CAAC;IAEvB,IAAIA,OAAO,CAAC,CAAC,CAAC,EAAE;MACf,MAAML,IAAI,GAAGF,cAAc,CAACC,IAAI,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC;MAC7CJ,OAAO,CAACO,IAAI,CAAC,CAACT,IAAI,CAAC,CAACqB,MAAM,CAACpB,IAAI,CAAC,CAAC;IAClC,CAAC,MAAM;MACNC,OAAO,CAACO,IAAI,CAAC,CAACT,IAAI,CAAC,CAAC;IACrB;EACD;EAEA,OAAOE,OAAO;AACf;AAEA,SAASoB,UAAUA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAClC,MAAMC,OAAO,GAAG,CAAC,CAAC;EAElB,KAAK,MAAMC,KAAK,IAAIF,MAAM,EAAE;IAC3B,KAAK,MAAMN,KAAK,IAAIQ,KAAK,CAACF,MAAM,EAAE;MACjCC,OAAO,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGQ,KAAK,CAACC,OAAO,GAAG,IAAI,GAAGT,KAAK,CAACrB,KAAK,CAAC,CAAC,CAAC;IAC1D;EACD;EAEA,IAAI+B,OAAO,GAAGL,KAAK;EACnB,KAAK,MAAMM,SAAS,IAAIC,MAAM,CAACC,IAAI,CAACN,OAAO,CAAC,EAAE;IAC7C,IAAIO,KAAK,CAACC,OAAO,CAACR,OAAO,CAACI,SAAS,CAAC,CAAC,EAAE;MACtC,IAAI,EAAEA,SAAS,IAAID,OAAO,CAAC,EAAE;QAC5B,MAAM,IAAIZ,KAAK,CAAC,wBAAwBa,SAAS,EAAE,CAAC;MACrD;MAEA,IAAIJ,OAAO,CAACI,SAAS,CAAC,CAACpC,MAAM,GAAG,CAAC,EAAE;QAClCmC,OAAO,GAAGA,OAAO,CAACC,SAAS,CAAC,CAACK,KAAK,CAACN,OAAO,EAAEH,OAAO,CAACI,SAAS,CAAC,CAAC;MAChE,CAAC,MAAM;QACND,OAAO,GAAGA,OAAO,CAACC,SAAS,CAAC;MAC7B;IACD;EACD;EAEA,OAAOD,OAAO;AACf;AAEAO,MAAM,CAACC,OAAO,GAAG,CAACb,KAAK,EAAEc,GAAG,KAAK;EAChC,MAAMb,MAAM,GAAG,EAAE;EACjB,MAAMrB,MAAM,GAAG,EAAE;EACjB,IAAII,KAAK,GAAG,EAAE;;EAEd;EACA8B,GAAG,CAACzB,OAAO,CAAC3B,cAAc,EAAE,CAAC4B,CAAC,EAAEyB,UAAU,EAAEX,OAAO,EAAET,KAAK,EAAEqB,KAAK,EAAExB,GAAG,KAAK;IAC1E,IAAIuB,UAAU,EAAE;MACf/B,KAAK,CAACE,IAAI,CAAClB,QAAQ,CAAC+C,UAAU,CAAC,CAAC;IACjC,CAAC,MAAM,IAAIpB,KAAK,EAAE;MACjB,MAAMsB,GAAG,GAAGjC,KAAK,CAACkC,IAAI,CAAC,EAAE,CAAC;MAC1BlC,KAAK,GAAG,EAAE;MACVJ,MAAM,CAACM,IAAI,CAACe,MAAM,CAAC/B,MAAM,KAAK,CAAC,GAAG+C,GAAG,GAAGlB,UAAU,CAACC,KAAK,EAAEC,MAAM,CAAC,CAACgB,GAAG,CAAC,CAAC;MACvEhB,MAAM,CAACf,IAAI,CAAC;QAACkB,OAAO;QAAEH,MAAM,EAAEP,UAAU,CAACC,KAAK;MAAC,CAAC,CAAC;IAClD,CAAC,MAAM,IAAIqB,KAAK,EAAE;MACjB,IAAIf,MAAM,CAAC/B,MAAM,KAAK,CAAC,EAAE;QACxB,MAAM,IAAIuB,KAAK,CAAC,8CAA8C,CAAC;MAChE;MAEAb,MAAM,CAACM,IAAI,CAACa,UAAU,CAACC,KAAK,EAAEC,MAAM,CAAC,CAACjB,KAAK,CAACkC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;MACtDlC,KAAK,GAAG,EAAE;MACViB,MAAM,CAACkB,GAAG,CAAC,CAAC;IACb,CAAC,MAAM;MACNnC,KAAK,CAACE,IAAI,CAACM,GAAG,CAAC;IAChB;EACD,CAAC,CAAC;EAEFZ,MAAM,CAACM,IAAI,CAACF,KAAK,CAACkC,IAAI,CAAC,EAAE,CAAC,CAAC;EAE3B,IAAIjB,MAAM,CAAC/B,MAAM,GAAG,CAAC,EAAE;IACtB,MAAMkD,MAAM,GAAG,qCAAqCnB,MAAM,CAAC/B,MAAM,mBAAmB+B,MAAM,CAAC/B,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,UAAU;IAC5H,MAAM,IAAIuB,KAAK,CAAC2B,MAAM,CAAC;EACxB;EAEA,OAAOxC,MAAM,CAACsC,IAAI,CAAC,EAAE,CAAC;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}