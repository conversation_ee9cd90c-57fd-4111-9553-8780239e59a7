{"ast": null, "code": "// دالة تنسيق العملة بالدينار الليبي\nexport const formatCurrency = amount => {\n  if (amount === null || amount === undefined || isNaN(amount)) {\n    return '0.00 د.ل';\n  }\n  return new Intl.NumberFormat('ar-LY', {\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  }).format(amount) + ' د.ل';\n};\n\n// دالة تحويل النص إلى رقم\nexport const parseAmount = value => {\n  if (!value) return 0;\n  const numericValue = parseFloat(value.toString().replace(/[^\\d.-]/g, ''));\n  return isNaN(numericValue) ? 0 : numericValue;\n};\n\n// دالة تنسيق الأرقام بدون رمز العملة\nexport const formatNumber = amount => {\n  if (amount === null || amount === undefined || isNaN(amount)) {\n    return '0';\n  }\n  return new Intl.NumberFormat('ar-LY').format(amount);\n};\n\n// دالة تنسيق التاريخ بالشكل الرقمي DD/MM/YYYY\nexport const formatDate = date => {\n  if (!date) return '';\n  const d = new Date(date);\n  if (isNaN(d.getTime())) return '';\n  const day = String(d.getDate()).padStart(2, '0');\n  const month = String(d.getMonth() + 1).padStart(2, '0');\n  const year = d.getFullYear();\n  return `${day}/${month}/${year}`;\n};\n\n// دالة تنسيق التاريخ والوقت\nexport const formatDateTime = date => {\n  if (!date) return '';\n  const d = new Date(date);\n  if (isNaN(d.getTime())) return '';\n  const day = String(d.getDate()).padStart(2, '0');\n  const month = String(d.getMonth() + 1).padStart(2, '0');\n  const year = d.getFullYear();\n  const hours = String(d.getHours()).padStart(2, '0');\n  const minutes = String(d.getMinutes()).padStart(2, '0');\n  return `${day}/${month}/${year} ${hours}:${minutes}`;\n};\n\n// دالة الحصول على التاريخ الحالي بالتنسيق المطلوب\nexport const getCurrentDate = () => {\n  return formatDate(new Date());\n};", "map": {"version": 3, "names": ["formatCurrency", "amount", "undefined", "isNaN", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "parseAmount", "value", "numericValue", "parseFloat", "toString", "replace", "formatNumber", "formatDate", "date", "d", "Date", "getTime", "day", "String", "getDate", "padStart", "month", "getMonth", "year", "getFullYear", "formatDateTime", "hours", "getHours", "minutes", "getMinutes", "getCurrentDate"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/utils/currency.js"], "sourcesContent": ["// دالة تنسيق العملة بالدينار الليبي\nexport const formatCurrency = (amount) => {\n  if (amount === null || amount === undefined || isNaN(amount)) {\n    return '0.00 د.ل';\n  }\n\n  return new Intl.NumberFormat('ar-LY', {\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  }).format(amount) + ' د.ل';\n};\n\n// دالة تحويل النص إلى رقم\nexport const parseAmount = (value) => {\n  if (!value) return 0;\n  const numericValue = parseFloat(value.toString().replace(/[^\\d.-]/g, ''));\n  return isNaN(numericValue) ? 0 : numericValue;\n};\n\n// دالة تنسيق الأرقام بدون رمز العملة\nexport const formatNumber = (amount) => {\n  if (amount === null || amount === undefined || isNaN(amount)) {\n    return '0';\n  }\n\n  return new Intl.NumberFormat('ar-LY').format(amount);\n};\n\n// دالة تنسيق التاريخ بالشكل الرقمي DD/MM/YYYY\nexport const formatDate = (date) => {\n  if (!date) return '';\n\n  const d = new Date(date);\n  if (isNaN(d.getTime())) return '';\n\n  const day = String(d.getDate()).padStart(2, '0');\n  const month = String(d.getMonth() + 1).padStart(2, '0');\n  const year = d.getFullYear();\n\n  return `${day}/${month}/${year}`;\n};\n\n// دالة تنسيق التاريخ والوقت\nexport const formatDateTime = (date) => {\n  if (!date) return '';\n\n  const d = new Date(date);\n  if (isNaN(d.getTime())) return '';\n\n  const day = String(d.getDate()).padStart(2, '0');\n  const month = String(d.getMonth() + 1).padStart(2, '0');\n  const year = d.getFullYear();\n  const hours = String(d.getHours()).padStart(2, '0');\n  const minutes = String(d.getMinutes()).padStart(2, '0');\n\n  return `${day}/${month}/${year} ${hours}:${minutes}`;\n};\n\n// دالة الحصول على التاريخ الحالي بالتنسيق المطلوب\nexport const getCurrentDate = () => {\n  return formatDate(new Date());\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,cAAc,GAAIC,MAAM,IAAK;EACxC,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKC,SAAS,IAAIC,KAAK,CAACF,MAAM,CAAC,EAAE;IAC5D,OAAO,UAAU;EACnB;EAEA,OAAO,IAAIG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IACpCC,qBAAqB,EAAE,CAAC;IACxBC,qBAAqB,EAAE;EACzB,CAAC,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC,GAAG,MAAM;AAC5B,CAAC;;AAED;AACA,OAAO,MAAMQ,WAAW,GAAIC,KAAK,IAAK;EACpC,IAAI,CAACA,KAAK,EAAE,OAAO,CAAC;EACpB,MAAMC,YAAY,GAAGC,UAAU,CAACF,KAAK,CAACG,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;EACzE,OAAOX,KAAK,CAACQ,YAAY,CAAC,GAAG,CAAC,GAAGA,YAAY;AAC/C,CAAC;;AAED;AACA,OAAO,MAAMI,YAAY,GAAId,MAAM,IAAK;EACtC,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKC,SAAS,IAAIC,KAAK,CAACF,MAAM,CAAC,EAAE;IAC5D,OAAO,GAAG;EACZ;EAEA,OAAO,IAAIG,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACG,MAAM,CAACP,MAAM,CAAC;AACtD,CAAC;;AAED;AACA,OAAO,MAAMe,UAAU,GAAIC,IAAI,IAAK;EAClC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EAEpB,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;EACxB,IAAId,KAAK,CAACe,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE;EAEjC,MAAMC,GAAG,GAAGC,MAAM,CAACJ,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAChD,MAAMC,KAAK,GAAGH,MAAM,CAACJ,CAAC,CAACQ,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACvD,MAAMG,IAAI,GAAGT,CAAC,CAACU,WAAW,CAAC,CAAC;EAE5B,OAAO,GAAGP,GAAG,IAAII,KAAK,IAAIE,IAAI,EAAE;AAClC,CAAC;;AAED;AACA,OAAO,MAAME,cAAc,GAAIZ,IAAI,IAAK;EACtC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EAEpB,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;EACxB,IAAId,KAAK,CAACe,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE;EAEjC,MAAMC,GAAG,GAAGC,MAAM,CAACJ,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAChD,MAAMC,KAAK,GAAGH,MAAM,CAACJ,CAAC,CAACQ,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACvD,MAAMG,IAAI,GAAGT,CAAC,CAACU,WAAW,CAAC,CAAC;EAC5B,MAAME,KAAK,GAAGR,MAAM,CAACJ,CAAC,CAACa,QAAQ,CAAC,CAAC,CAAC,CAACP,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACnD,MAAMQ,OAAO,GAAGV,MAAM,CAACJ,CAAC,CAACe,UAAU,CAAC,CAAC,CAAC,CAACT,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAEvD,OAAO,GAAGH,GAAG,IAAII,KAAK,IAAIE,IAAI,IAAIG,KAAK,IAAIE,OAAO,EAAE;AACtD,CAAC;;AAED;AACA,OAAO,MAAME,cAAc,GAAGA,CAAA,KAAM;EAClC,OAAOlB,UAAU,CAAC,IAAIG,IAAI,CAAC,CAAC,CAAC;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}