{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\AccountStatements.js\";\nimport React, { useState, useEffect } from 'react';\nimport { formatCurrency } from '../utils/currency';\nconst AccountStatements = () => {\n  const [statements, setStatements] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    description: '',\n    debit: '',\n    credit: '',\n    accountType: 'عام'\n  });\n  useEffect(() => {\n    // جلب البيانات من قاعدة البيانات\n    // مؤقتاً سنستخدم بيانات تجريبية\n    setStatements([{\n      id: 1,\n      date: '2024-01-15',\n      description: 'إيداع نقدي',\n      debit: 0,\n      credit: 25000,\n      balance: 25000,\n      accountType: 'خزينة'\n    }, {\n      id: 2,\n      date: '2024-01-14',\n      description: 'مشتريات مواد خام',\n      debit: 8500,\n      credit: 0,\n      balance: 16500,\n      accountType: 'مخزن'\n    }, {\n      id: 3,\n      date: '2024-01-13',\n      description: 'مصروفات نقل',\n      debit: 3200,\n      credit: 0,\n      balance: 13300,\n      accountType: 'نقل'\n    }]);\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    const newStatement = {\n      id: statements.length + 1,\n      ...formData,\n      debit: parseFloat(formData.debit) || 0,\n      credit: parseFloat(formData.credit) || 0,\n      balance: calculateNewBalance()\n    };\n    setStatements(prev => [newStatement, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      description: '',\n      debit: '',\n      credit: '',\n      accountType: 'عام'\n    });\n    setShowForm(false);\n  };\n  const calculateNewBalance = () => {\n    const lastBalance = statements.length > 0 ? statements[0].balance : 0;\n    const debit = parseFloat(formData.debit) || 0;\n    const credit = parseFloat(formData.credit) || 0;\n    return lastBalance + credit - debit;\n  };\n  const getTotalDebit = () => {\n    return statements.reduce((sum, statement) => sum + statement.debit, 0);\n  };\n  const getTotalCredit = () => {\n    return statements.reduce((sum, statement) => sum + statement.credit, 0);\n  };\n  const getCurrentBalance = () => {\n    return getTotalCredit() - getTotalDebit();\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"account-statements\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stats-grid\",\n    style: {\n      marginBottom: '30px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-value\",\n    style: {\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 11\n    }\n  }, formatCurrency(getTotalDebit())), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 11\n    }\n  }, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u062F\\u064A\\u0646\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-value\",\n    style: {\n      color: '#ffa500'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 11\n    }\n  }, formatCurrency(getTotalCredit())), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 11\n    }\n  }, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u062F\\u0627\\u0626\\u0646\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-value\",\n    style: {\n      color: '#1e3a8a'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 11\n    }\n  }, formatCurrency(getCurrentBalance())), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 11\n    }\n  }, \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    className: \"card-title\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 11\n    }\n  }, \"\\u0643\\u0634\\u0641 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    onClick: () => setShowForm(!showForm),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 11\n    }\n  }, \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0642\\u064A\\u062F \\u062C\\u062F\\u064A\\u062F\")), showForm && /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    style: {\n      marginBottom: '20px',\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-row\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"date\",\n    name: \"date\",\n    value: formData.date,\n    onChange: handleInputChange,\n    className: \"form-input\",\n    required: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 17\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 17\n    }\n  }, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"), /*#__PURE__*/React.createElement(\"select\", {\n    name: \"accountType\",\n    value: formData.accountType,\n    onChange: handleInputChange,\n    className: \"form-input\",\n    required: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\\u0639\\u0627\\u0645\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 19\n    }\n  }, \"\\u0639\\u0627\\u0645\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\\u062E\\u0632\\u064A\\u0646\\u0629\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 19\n    }\n  }, \"\\u062E\\u0632\\u064A\\u0646\\u0629\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\\u0645\\u062E\\u0632\\u0646\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 19\n    }\n  }, \"\\u0645\\u062E\\u0632\\u0646\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\\u0646\\u0642\\u0644\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 19\n    }\n  }, \"\\u0646\\u0642\\u0644\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\\u0645\\u0639\\u064A\\u0634\\u0629\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 19\n    }\n  }, \"\\u0645\\u0639\\u064A\\u0634\\u0629\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\\u0637\\u0644\\u0627\\u0621\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 19\n    }\n  }, \"\\u0637\\u0644\\u0627\\u0621\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\\u0645\\u0635\\u0646\\u0639\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 19\n    }\n  }, \"\\u0645\\u0635\\u0646\\u0639\")))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 15\n    }\n  }, \"\\u0627\\u0644\\u0648\\u0635\\u0641\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    name: \"description\",\n    value: formData.description,\n    onChange: handleInputChange,\n    className: \"form-input\",\n    placeholder: \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0645\\u0639\\u0627\\u0645\\u0644\\u0629\",\n    required: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-row\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 17\n    }\n  }, \"\\u0645\\u062F\\u064A\\u0646\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"number\",\n    name: \"debit\",\n    value: formData.debit,\n    onChange: handleInputChange,\n    className: \"form-input\",\n    placeholder: \"0.00\",\n    step: \"0.01\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 17\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    className: \"form-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 17\n    }\n  }, \"\\u062F\\u0627\\u0626\\u0646\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"number\",\n    name: \"credit\",\n    value: formData.credit,\n    onChange: handleInputChange,\n    className: \"form-input\",\n    placeholder: \"0.00\",\n    step: \"0.01\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 17\n    }\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 15\n    }\n  }, \"\\uD83D\\uDCBE \\u062D\\u0641\\u0638 \\u0627\\u0644\\u0642\\u064A\\u062F\"), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"btn btn-secondary\",\n    onClick: () => setShowForm(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 15\n    }\n  }, \"\\u274C \\u0625\\u0644\\u063A\\u0627\\u0621\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u0648\\u0635\\u0641\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 17\n    }\n  }, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 17\n    }\n  }, \"\\u0645\\u062F\\u064A\\u0646\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 17\n    }\n  }, \"\\u062F\\u0627\\u0626\\u0646\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 17\n    }\n  }, \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 13\n    }\n  }, statements.map(statement => /*#__PURE__*/React.createElement(\"tr\", {\n    key: statement.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 19\n    }\n  }, new Date(statement.date).toLocaleDateString('ar-SA')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 19\n    }\n  }, statement.description), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"badge badge-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 21\n    }\n  }, statement.accountType)), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      color: statement.debit > 0 ? '#dc3545' : '#666'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 19\n    }\n  }, statement.debit > 0 ? formatCurrency(statement.debit) : '-'), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      color: statement.credit > 0 ? '#ffa500' : '#666'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 19\n    }\n  }, statement.credit > 0 ? formatCurrency(statement.credit) : '-'), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      fontWeight: 'bold',\n      color: statement.balance >= 0 ? '#1e3a8a' : '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 19\n    }\n  }, formatCurrency(statement.balance)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-secondary\",\n    style: {\n      padding: '5px 10px',\n      fontSize: '12px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 21\n    }\n  }, \"\\u270F\\uFE0F \\u062A\\u0639\\u062F\\u064A\\u0644\")))))))));\n};\nexport default AccountStatements;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "formatCurrency", "AccountStatements", "statements", "setStatements", "showForm", "setShowForm", "formData", "setFormData", "date", "Date", "toISOString", "split", "description", "debit", "credit", "accountType", "id", "balance", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "newStatement", "length", "parseFloat", "calculateNewBalance", "lastBalance", "getTotalDebit", "reduce", "sum", "statement", "getTotalCredit", "getCurrentBalance", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginBottom", "color", "onClick", "onSubmit", "padding", "backgroundColor", "borderRadius", "type", "onChange", "required", "placeholder", "step", "display", "gap", "map", "key", "toLocaleDateString", "fontWeight", "fontSize"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/AccountStatements.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { formatCurrency } from '../utils/currency';\n\nconst AccountStatements = () => {\n  const [statements, setStatements] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    description: '',\n    debit: '',\n    credit: '',\n    accountType: 'عام'\n  });\n\n  useEffect(() => {\n    // جلب البيانات من قاعدة البيانات\n    // مؤقتاً سنستخدم بيانات تجريبية\n    setStatements([\n      {\n        id: 1,\n        date: '2024-01-15',\n        description: 'إيداع نقدي',\n        debit: 0,\n        credit: 25000,\n        balance: 25000,\n        accountType: 'خزينة'\n      },\n      {\n        id: 2,\n        date: '2024-01-14',\n        description: 'مشتريات مواد خام',\n        debit: 8500,\n        credit: 0,\n        balance: 16500,\n        accountType: 'مخزن'\n      },\n      {\n        id: 3,\n        date: '2024-01-13',\n        description: 'مصروفات نقل',\n        debit: 3200,\n        credit: 0,\n        balance: 13300,\n        accountType: 'نقل'\n      }\n    ]);\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    const newStatement = {\n      id: statements.length + 1,\n      ...formData,\n      debit: parseFloat(formData.debit) || 0,\n      credit: parseFloat(formData.credit) || 0,\n      balance: calculateNewBalance()\n    };\n\n    setStatements(prev => [newStatement, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      description: '',\n      debit: '',\n      credit: '',\n      accountType: 'عام'\n    });\n    setShowForm(false);\n  };\n\n  const calculateNewBalance = () => {\n    const lastBalance = statements.length > 0 ? statements[0].balance : 0;\n    const debit = parseFloat(formData.debit) || 0;\n    const credit = parseFloat(formData.credit) || 0;\n    return lastBalance + credit - debit;\n  };\n\n\n\n  const getTotalDebit = () => {\n    return statements.reduce((sum, statement) => sum + statement.debit, 0);\n  };\n\n  const getTotalCredit = () => {\n    return statements.reduce((sum, statement) => sum + statement.credit, 0);\n  };\n\n  const getCurrentBalance = () => {\n    return getTotalCredit() - getTotalDebit();\n  };\n\n  return (\n    <div className=\"account-statements\">\n      {/* ملخص الحساب */}\n      <div className=\"stats-grid\" style={{ marginBottom: '30px' }}>\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#dc3545' }}>\n            {formatCurrency(getTotalDebit())}\n          </div>\n          <div className=\"stat-label\">إجمالي المدين</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#ffa500' }}>\n            {formatCurrency(getTotalCredit())}\n          </div>\n          <div className=\"stat-label\">إجمالي الدائن</div>\n        </div>\n\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#1e3a8a' }}>\n            {formatCurrency(getCurrentBalance())}\n          </div>\n          <div className=\"stat-label\">الرصيد الحالي</div>\n        </div>\n      </div>\n\n      {/* كشف الحساب */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">كشف الحساب</h3>\n          <button \n            className=\"btn btn-primary\"\n            onClick={() => setShowForm(!showForm)}\n          >\n            ➕ إضافة قيد جديد\n          </button>\n        </div>\n\n        {/* نموذج إضافة قيد */}\n        {showForm && (\n          <form onSubmit={handleSubmit} style={{ marginBottom: '20px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">التاريخ</label>\n                <input\n                  type=\"date\"\n                  name=\"date\"\n                  value={formData.date}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  required\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">نوع الحساب</label>\n                <select\n                  name=\"accountType\"\n                  value={formData.accountType}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  required\n                >\n                  <option value=\"عام\">عام</option>\n                  <option value=\"خزينة\">خزينة</option>\n                  <option value=\"مخزن\">مخزن</option>\n                  <option value=\"نقل\">نقل</option>\n                  <option value=\"معيشة\">معيشة</option>\n                  <option value=\"طلاء\">طلاء</option>\n                  <option value=\"مصنع\">مصنع</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">الوصف</label>\n              <input\n                type=\"text\"\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"وصف المعاملة\"\n                required\n              />\n            </div>\n\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">مدين</label>\n                <input\n                  type=\"number\"\n                  name=\"debit\"\n                  value={formData.debit}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"0.00\"\n                  step=\"0.01\"\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">دائن</label>\n                <input\n                  type=\"number\"\n                  name=\"credit\"\n                  value={formData.credit}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"0.00\"\n                  step=\"0.01\"\n                />\n              </div>\n            </div>\n\n            <div style={{ display: 'flex', gap: '10px' }}>\n              <button type=\"submit\" className=\"btn btn-primary\">\n                💾 حفظ القيد\n              </button>\n              <button \n                type=\"button\" \n                className=\"btn btn-secondary\"\n                onClick={() => setShowForm(false)}\n              >\n                ❌ إلغاء\n              </button>\n            </div>\n          </form>\n        )}\n\n        {/* جدول كشف الحساب */}\n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>التاريخ</th>\n                <th>الوصف</th>\n                <th>نوع الحساب</th>\n                <th>مدين</th>\n                <th>دائن</th>\n                <th>الرصيد</th>\n                <th>إجراءات</th>\n              </tr>\n            </thead>\n            <tbody>\n              {statements.map((statement) => (\n                <tr key={statement.id}>\n                  <td>{new Date(statement.date).toLocaleDateString('ar-SA')}</td>\n                  <td>{statement.description}</td>\n                  <td>\n                    <span className=\"badge badge-info\">{statement.accountType}</span>\n                  </td>\n                  <td style={{ color: statement.debit > 0 ? '#dc3545' : '#666' }}>\n                    {statement.debit > 0 ? formatCurrency(statement.debit) : '-'}\n                  </td>\n                  <td style={{ color: statement.credit > 0 ? '#ffa500' : '#666' }}>\n                    {statement.credit > 0 ? formatCurrency(statement.credit) : '-'}\n                  </td>\n                  <td style={{\n                    fontWeight: 'bold',\n                    color: statement.balance >= 0 ? '#1e3a8a' : '#dc3545'\n                  }}>\n                    {formatCurrency(statement.balance)}\n                  </td>\n                  <td>\n                    <button className=\"btn btn-secondary\" style={{ padding: '5px 10px', fontSize: '12px' }}>\n                      ✏️ تعديل\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AccountStatements;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,mBAAmB;AAElD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGL,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACM,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5CC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;EACf,CAAC,CAAC;EAEFhB,SAAS,CAAC,MAAM;IACd;IACA;IACAI,aAAa,CAAC,CACZ;MACEa,EAAE,EAAE,CAAC;MACLR,IAAI,EAAE,YAAY;MAClBI,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,KAAK;MACbG,OAAO,EAAE,KAAK;MACdF,WAAW,EAAE;IACf,CAAC,EACD;MACEC,EAAE,EAAE,CAAC;MACLR,IAAI,EAAE,YAAY;MAClBI,WAAW,EAAE,kBAAkB;MAC/BC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,CAAC;MACTG,OAAO,EAAE,KAAK;MACdF,WAAW,EAAE;IACf,CAAC,EACD;MACEC,EAAE,EAAE,CAAC;MACLR,IAAI,EAAE,YAAY;MAClBI,WAAW,EAAE,aAAa;MAC1BC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,CAAC;MACTG,OAAO,EAAE,KAAK;MACdF,WAAW,EAAE;IACf,CAAC,CACF,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCf,WAAW,CAACgB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAIL,CAAC,IAAK;IAC1BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,MAAMC,YAAY,GAAG;MACnBV,EAAE,EAAEd,UAAU,CAACyB,MAAM,GAAG,CAAC;MACzB,GAAGrB,QAAQ;MACXO,KAAK,EAAEe,UAAU,CAACtB,QAAQ,CAACO,KAAK,CAAC,IAAI,CAAC;MACtCC,MAAM,EAAEc,UAAU,CAACtB,QAAQ,CAACQ,MAAM,CAAC,IAAI,CAAC;MACxCG,OAAO,EAAEY,mBAAmB,CAAC;IAC/B,CAAC;IAED1B,aAAa,CAACoB,IAAI,IAAI,CAACG,YAAY,EAAE,GAAGH,IAAI,CAAC,CAAC;IAC9ChB,WAAW,CAAC;MACVC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE;IACf,CAAC,CAAC;IACFV,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMwB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,WAAW,GAAG5B,UAAU,CAACyB,MAAM,GAAG,CAAC,GAAGzB,UAAU,CAAC,CAAC,CAAC,CAACe,OAAO,GAAG,CAAC;IACrE,MAAMJ,KAAK,GAAGe,UAAU,CAACtB,QAAQ,CAACO,KAAK,CAAC,IAAI,CAAC;IAC7C,MAAMC,MAAM,GAAGc,UAAU,CAACtB,QAAQ,CAACQ,MAAM,CAAC,IAAI,CAAC;IAC/C,OAAOgB,WAAW,GAAGhB,MAAM,GAAGD,KAAK;EACrC,CAAC;EAID,MAAMkB,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAO7B,UAAU,CAAC8B,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAKD,GAAG,GAAGC,SAAS,CAACrB,KAAK,EAAE,CAAC,CAAC;EACxE,CAAC;EAED,MAAMsB,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOjC,UAAU,CAAC8B,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAKD,GAAG,GAAGC,SAAS,CAACpB,MAAM,EAAE,CAAC,CAAC;EACzE,CAAC;EAED,MAAMsB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAOD,cAAc,CAAC,CAAC,GAAGJ,aAAa,CAAC,CAAC;EAC3C,CAAC;EAED,oBACElC,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEjC/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEC,YAAY,EAAE;IAAO,CAAE;IAAAP,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1D/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACrD5C,cAAc,CAAC+B,aAAa,CAAC,CAAC,CAC5B,CAAC,eACNlC,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2EAAkB,CAC3C,CAAC,eAEN/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACrD5C,cAAc,CAACmC,cAAc,CAAC,CAAC,CAC7B,CAAC,eACNtC,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2EAAkB,CAC3C,CAAC,eAEN/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEE,KAAK,EAAE;IAAU,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACrD5C,cAAc,CAACoC,iBAAiB,CAAC,CAAC,CAChC,CAAC,eACNvC,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2EAAkB,CAC3C,CACF,CAAC,eAGN/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnB/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B/C,KAAA,CAAAwC,aAAA;IAAIC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yDAAc,CAAC,eAC1C/C,KAAA,CAAAwC,aAAA;IACEC,SAAS,EAAC,iBAAiB;IAC3BU,OAAO,EAAEA,CAAA,KAAM3C,WAAW,CAAC,CAACD,QAAQ,CAAE;IAAAmC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvC,mFAEO,CACL,CAAC,EAGLxC,QAAQ,iBACPP,KAAA,CAAAwC,aAAA;IAAMY,QAAQ,EAAEzB,YAAa;IAACqB,KAAK,EAAE;MAAEC,YAAY,EAAE,MAAM;MAAEI,OAAO,EAAE,MAAM;MAAEC,eAAe,EAAE,SAAS;MAAEC,YAAY,EAAE;IAAM,CAAE;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9H/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB/C,KAAA,CAAAwC,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,4CAAc,CAAC,eAC7C/C,KAAA,CAAAwC,aAAA;IACEgB,IAAI,EAAC,MAAM;IACXjC,IAAI,EAAC,MAAM;IACXC,KAAK,EAAEf,QAAQ,CAACE,IAAK;IACrB8C,QAAQ,EAAEpC,iBAAkB;IAC5BoB,SAAS,EAAC,YAAY;IACtBiB,QAAQ;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACT,CACE,CAAC,eAEN/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB/C,KAAA,CAAAwC,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yDAAiB,CAAC,eAChD/C,KAAA,CAAAwC,aAAA;IACEjB,IAAI,EAAC,aAAa;IAClBC,KAAK,EAAEf,QAAQ,CAACS,WAAY;IAC5BuC,QAAQ,EAAEpC,iBAAkB;IAC5BoB,SAAS,EAAC,YAAY;IACtBiB,QAAQ;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAER/C,KAAA,CAAAwC,aAAA;IAAQhB,KAAK,EAAC,oBAAK;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oBAAW,CAAC,eAChC/C,KAAA,CAAAwC,aAAA;IAAQhB,KAAK,EAAC,gCAAO;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAAa,CAAC,eACpC/C,KAAA,CAAAwC,aAAA;IAAQhB,KAAK,EAAC,0BAAM;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,0BAAY,CAAC,eAClC/C,KAAA,CAAAwC,aAAA;IAAQhB,KAAK,EAAC,oBAAK;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oBAAW,CAAC,eAChC/C,KAAA,CAAAwC,aAAA;IAAQhB,KAAK,EAAC,gCAAO;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAAa,CAAC,eACpC/C,KAAA,CAAAwC,aAAA;IAAQhB,KAAK,EAAC,0BAAM;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,0BAAY,CAAC,eAClC/C,KAAA,CAAAwC,aAAA;IAAQhB,KAAK,EAAC,0BAAM;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,0BAAY,CAC3B,CACL,CACF,CAAC,eAEN/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB/C,KAAA,CAAAwC,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAAY,CAAC,eAC3C/C,KAAA,CAAAwC,aAAA;IACEgB,IAAI,EAAC,MAAM;IACXjC,IAAI,EAAC,aAAa;IAClBC,KAAK,EAAEf,QAAQ,CAACM,WAAY;IAC5B0C,QAAQ,EAAEpC,iBAAkB;IAC5BoB,SAAS,EAAC,YAAY;IACtBkB,WAAW,EAAC,qEAAc;IAC1BD,QAAQ;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACT,CACE,CAAC,eAEN/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB/C,KAAA,CAAAwC,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,0BAAW,CAAC,eAC1C/C,KAAA,CAAAwC,aAAA;IACEgB,IAAI,EAAC,QAAQ;IACbjC,IAAI,EAAC,OAAO;IACZC,KAAK,EAAEf,QAAQ,CAACO,KAAM;IACtByC,QAAQ,EAAEpC,iBAAkB;IAC5BoB,SAAS,EAAC,YAAY;IACtBkB,WAAW,EAAC,MAAM;IAClBC,IAAI,EAAC,MAAM;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACZ,CACE,CAAC,eAEN/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB/C,KAAA,CAAAwC,aAAA;IAAOC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,0BAAW,CAAC,eAC1C/C,KAAA,CAAAwC,aAAA;IACEgB,IAAI,EAAC,QAAQ;IACbjC,IAAI,EAAC,QAAQ;IACbC,KAAK,EAAEf,QAAQ,CAACQ,MAAO;IACvBwC,QAAQ,EAAEpC,iBAAkB;IAC5BoB,SAAS,EAAC,YAAY;IACtBkB,WAAW,EAAC,MAAM;IAClBC,IAAI,EAAC,MAAM;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACZ,CACE,CACF,CAAC,eAEN/C,KAAA,CAAAwC,aAAA;IAAKQ,KAAK,EAAE;MAAEa,OAAO,EAAE,MAAM;MAAEC,GAAG,EAAE;IAAO,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3C/C,KAAA,CAAAwC,aAAA;IAAQgB,IAAI,EAAC,QAAQ;IAACf,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gEAE1C,CAAC,eACT/C,KAAA,CAAAwC,aAAA;IACEgB,IAAI,EAAC,QAAQ;IACbf,SAAS,EAAC,mBAAmB;IAC7BU,OAAO,EAAEA,CAAA,KAAM3C,WAAW,CAAC,KAAK,CAAE;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACnC,uCAEO,CACL,CACD,CACP,eAGD/C,KAAA,CAAAwC,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B/C,KAAA,CAAAwC,aAAA;IAAOC,SAAS,EAAC,OAAO;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtB/C,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE/C,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE/C,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,4CAAW,CAAC,eAChB/C,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,gCAAS,CAAC,eACd/C,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yDAAc,CAAC,eACnB/C,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,0BAAQ,CAAC,eACb/C,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,0BAAQ,CAAC,eACb/C,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sCAAU,CAAC,eACf/C,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,4CAAW,CACb,CACC,CAAC,eACR/C,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG1C,UAAU,CAAC0D,GAAG,CAAE1B,SAAS,iBACxBrC,KAAA,CAAAwC,aAAA;IAAIwB,GAAG,EAAE3B,SAAS,CAAClB,EAAG;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpB/C,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,IAAInC,IAAI,CAACyB,SAAS,CAAC1B,IAAI,CAAC,CAACsD,kBAAkB,CAAC,OAAO,CAAM,CAAC,eAC/DjE,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKV,SAAS,CAACtB,WAAgB,CAAC,eAChCf,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE/C,KAAA,CAAAwC,aAAA;IAAMC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEV,SAAS,CAACnB,WAAkB,CAC9D,CAAC,eACLlB,KAAA,CAAAwC,aAAA;IAAIQ,KAAK,EAAE;MAAEE,KAAK,EAAEb,SAAS,CAACrB,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG;IAAO,CAAE;IAAA0B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5DV,SAAS,CAACrB,KAAK,GAAG,CAAC,GAAGb,cAAc,CAACkC,SAAS,CAACrB,KAAK,CAAC,GAAG,GACvD,CAAC,eACLhB,KAAA,CAAAwC,aAAA;IAAIQ,KAAK,EAAE;MAAEE,KAAK,EAAEb,SAAS,CAACpB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;IAAO,CAAE;IAAAyB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7DV,SAAS,CAACpB,MAAM,GAAG,CAAC,GAAGd,cAAc,CAACkC,SAAS,CAACpB,MAAM,CAAC,GAAG,GACzD,CAAC,eACLjB,KAAA,CAAAwC,aAAA;IAAIQ,KAAK,EAAE;MACTkB,UAAU,EAAE,MAAM;MAClBhB,KAAK,EAAEb,SAAS,CAACjB,OAAO,IAAI,CAAC,GAAG,SAAS,GAAG;IAC9C,CAAE;IAAAsB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACC5C,cAAc,CAACkC,SAAS,CAACjB,OAAO,CAC/B,CAAC,eACLpB,KAAA,CAAAwC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE/C,KAAA,CAAAwC,aAAA;IAAQC,SAAS,EAAC,mBAAmB;IAACO,KAAK,EAAE;MAAEK,OAAO,EAAE,UAAU;MAAEc,QAAQ,EAAE;IAAO,CAAE;IAAAzB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6CAEhF,CACN,CACF,CACL,CACI,CACF,CACJ,CACF,CACF,CAAC;AAEV,CAAC;AAED,eAAe3C,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}