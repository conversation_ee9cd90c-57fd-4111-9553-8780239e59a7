{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\PaintExpenses.js\";\nimport React, { useState } from 'react';\nconst PaintExpenses = () => {\n  const [expenses, setExpenses] = useState([{\n    id: 1,\n    date: '2024-01-15',\n    paintType: 'دهان أكريليك',\n    quantity: 50,\n    unitPrice: 45,\n    totalAmount: 2250,\n    supplier: 'شركة الدهانات المتقدمة',\n    projectName: 'مشروع المصنع الجديد',\n    notes: 'جودة عالية مقاوم للرطوبة'\n  }]);\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"paint-expenses\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    className: \"card-title\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 11\n    }\n  }, \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0637\\u0644\\u0627\\u0621\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 11\n    }\n  }, \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0635\\u0631\\u0648\\u0641 \\u0637\\u0644\\u0627\\u0621\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 17\n    }\n  }, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0621\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 17\n    }\n  }, \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 17\n    }\n  }, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 17\n    }\n  }, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0634\\u0631\\u0648\\u0639\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 17\n    }\n  }, \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }\n  }, expenses.map(expense => /*#__PURE__*/React.createElement(\"tr\", {\n    key: expense.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 19\n    }\n  }, new Date(expense.date).toLocaleDateString('ar-SA')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 19\n    }\n  }, expense.paintType), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 19\n    }\n  }, expense.quantity), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 19\n    }\n  }, formatCurrency(expense.unitPrice)), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      fontWeight: 'bold',\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 19\n    }\n  }, formatCurrency(expense.totalAmount)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 19\n    }\n  }, expense.supplier), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 19\n    }\n  }, expense.projectName), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-secondary\",\n    style: {\n      padding: '5px 10px',\n      fontSize: '12px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 21\n    }\n  }, \"\\u270F\\uFE0F \\u062A\\u0639\\u062F\\u064A\\u0644\")))))))));\n};\nexport default PaintExpenses;", "map": {"version": 3, "names": ["React", "useState", "PaintExpenses", "expenses", "setExpenses", "id", "date", "paintType", "quantity", "unitPrice", "totalAmount", "supplier", "projectName", "notes", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "expense", "key", "Date", "toLocaleDateString", "fontWeight", "color", "padding", "fontSize"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/PaintExpenses.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst PaintExpenses = () => {\n  const [expenses, setExpenses] = useState([\n    {\n      id: 1,\n      date: '2024-01-15',\n      paintType: 'دهان أكريليك',\n      quantity: 50,\n      unitPrice: 45,\n      totalAmount: 2250,\n      supplier: 'شركة الدهانات المتقدمة',\n      projectName: 'مشروع المصنع الجديد',\n      notes: 'جودة عالية مقاوم للرطوبة'\n    }\n  ]);\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  return (\n    <div className=\"paint-expenses\">\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">مصروفات الطلاء</h3>\n          <button className=\"btn btn-primary\">\n            ➕ إضافة مصروف طلاء\n          </button>\n        </div>\n\n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>التاريخ</th>\n                <th>نوع الطلاء</th>\n                <th>الكمية</th>\n                <th>سعر الوحدة</th>\n                <th>إجمالي المبلغ</th>\n                <th>المورد</th>\n                <th>اسم المشروع</th>\n                <th>إجراءات</th>\n              </tr>\n            </thead>\n            <tbody>\n              {expenses.map((expense) => (\n                <tr key={expense.id}>\n                  <td>{new Date(expense.date).toLocaleDateString('ar-SA')}</td>\n                  <td>{expense.paintType}</td>\n                  <td>{expense.quantity}</td>\n                  <td>{formatCurrency(expense.unitPrice)}</td>\n                  <td style={{ fontWeight: 'bold', color: '#dc3545' }}>\n                    {formatCurrency(expense.totalAmount)}\n                  </td>\n                  <td>{expense.supplier}</td>\n                  <td>{expense.projectName}</td>\n                  <td>\n                    <button className=\"btn btn-secondary\" style={{ padding: '5px 10px', fontSize: '12px' }}>\n                      ✏️ تعديل\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PaintExpenses;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAEvC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGH,QAAQ,CAAC,CACvC;IACEI,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE,cAAc;IACzBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,wBAAwB;IAClCC,WAAW,EAAE,qBAAqB;IAClCC,KAAK,EAAE;EACT,CAAC,CACF,CAAC;EAEF,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,oBACEf,KAAA,CAAAqB,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B5B,KAAA,CAAAqB,aAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnB5B,KAAA,CAAAqB,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B5B,KAAA,CAAAqB,aAAA;IAAIC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,iFAAkB,CAAC,eAC9C5B,KAAA,CAAAqB,aAAA;IAAQC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+FAE5B,CACL,CAAC,eAEN5B,KAAA,CAAAqB,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B5B,KAAA,CAAAqB,aAAA;IAAOC,SAAS,EAAC,OAAO;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtB5B,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE5B,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE5B,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,4CAAW,CAAC,eAChB5B,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yDAAc,CAAC,eACnB5B,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sCAAU,CAAC,eACf5B,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yDAAc,CAAC,eACnB5B,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,2EAAiB,CAAC,eACtB5B,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sCAAU,CAAC,eACf5B,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,+DAAe,CAAC,eACpB5B,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,4CAAW,CACb,CACC,CAAC,eACR5B,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGzB,QAAQ,CAAC0B,GAAG,CAAEC,OAAO,iBACpB9B,KAAA,CAAAqB,aAAA;IAAIU,GAAG,EAAED,OAAO,CAACzB,EAAG;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClB5B,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,IAAII,IAAI,CAACF,OAAO,CAACxB,IAAI,CAAC,CAAC2B,kBAAkB,CAAC,OAAO,CAAM,CAAC,eAC7DjC,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKE,OAAO,CAACvB,SAAc,CAAC,eAC5BP,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKE,OAAO,CAACtB,QAAa,CAAC,eAC3BR,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKd,cAAc,CAACgB,OAAO,CAACrB,SAAS,CAAM,CAAC,eAC5CT,KAAA,CAAAqB,aAAA;IAAIH,KAAK,EAAE;MAAEgB,UAAU,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjDd,cAAc,CAACgB,OAAO,CAACpB,WAAW,CACjC,CAAC,eACLV,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKE,OAAO,CAACnB,QAAa,CAAC,eAC3BX,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKE,OAAO,CAAClB,WAAgB,CAAC,eAC9BZ,KAAA,CAAAqB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE5B,KAAA,CAAAqB,aAAA;IAAQC,SAAS,EAAC,mBAAmB;IAACJ,KAAK,EAAE;MAAEkB,OAAO,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAO,CAAE;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6CAEhF,CACN,CACF,CACL,CACI,CACF,CACJ,CACF,CACF,CAAC;AAEV,CAAC;AAED,eAAe1B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}