import React from 'react';
import { useLocation } from 'react-router-dom';
import { formatDate, formatDateTime } from '../utils/currency';

const Header = () => {
  const location = useLocation();

  const getPageTitle = () => {
    const titles = {
      '/': 'لوحة التحكم',
      '/account-statements': 'كشف الحساب',
      '/warehouse-purchases': 'مشتريات المخزن',
      '/transport-expenses': 'مصروفات النقل',
      '/living-expenses': 'مصروفات المعيشة',
      '/paint-expenses': 'مصروفات الطلاء',
      '/factory-expenses': 'مصروفات المصنع',
      '/employee-withdrawals': 'مسحوبات الموظفين',
      '/treasury-deposits': 'إيداعات الخزينة',
      '/reports': 'التقارير'
    };
    return titles[location.pathname] || 'نظام المحاسبة';
  };

  const getCurrentDate = () => {
    return formatDate(new Date());
  };

  const getCurrentTime = () => {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  };

  return (
    <header className="header">
      <div className="header-info">
        <h1 className="page-title">{getPageTitle()}</h1>
        <div className="date-time-info">
          <span className="current-date">📅 {getCurrentDate()}</span>
          <span className="current-time">🕐 {getCurrentTime()}</span>
        </div>
      </div>

      <div className="header-actions">
        <div className="user-info">
          <span className="user-name">👤 المدير العام</span>
        </div>
        <button className="btn btn-secondary">
          ⚙️ الإعدادات
        </button>
        <button className="btn btn-primary">
          💾 حفظ
        </button>
        <button className="btn btn-success">
          📊 تقرير سريع
        </button>
      </div>
    </header>
  );
};

export default Header;
