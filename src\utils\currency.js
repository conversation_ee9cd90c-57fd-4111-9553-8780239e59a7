// دالة تنسيق العملة بالدينار الليبي
export const formatCurrency = (amount) => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '0.00 د.ل';
  }

  return new Intl.NumberFormat('ar-LY', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount) + ' د.ل';
};

// دالة تحويل النص إلى رقم
export const parseAmount = (value) => {
  if (!value) return 0;
  const numericValue = parseFloat(value.toString().replace(/[^\d.-]/g, ''));
  return isNaN(numericValue) ? 0 : numericValue;
};

// دالة تنسيق الأرقام بدون رمز العملة
export const formatNumber = (amount) => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '0';
  }

  return new Intl.NumberFormat('ar-LY').format(amount);
};

// دالة تنسيق التاريخ بالشكل الرقمي DD/MM/YYYY
export const formatDate = (date) => {
  if (!date) return '';

  const d = new Date(date);
  if (isNaN(d.getTime())) return '';

  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();

  return `${day}/${month}/${year}`;
};

// دالة تنسيق التاريخ والوقت
export const formatDateTime = (date) => {
  if (!date) return '';

  const d = new Date(date);
  if (isNaN(d.getTime())) return '';

  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');

  return `${day}/${month}/${year} ${hours}:${minutes}`;
};

// دالة الحصول على التاريخ الحالي بالتنسيق المطلوب
export const getCurrentDate = () => {
  return formatDate(new Date());
};
