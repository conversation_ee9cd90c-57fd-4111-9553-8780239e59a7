{"ast": null, "code": "'use strict';\n\nvar globalThis = require('../internals/global-this');\n\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\nmodule.exports = function (key, value) {\n  try {\n    defineProperty(globalThis, key, {\n      value: value,\n      configurable: true,\n      writable: true\n    });\n  } catch (error) {\n    globalThis[key] = value;\n  }\n  return value;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}