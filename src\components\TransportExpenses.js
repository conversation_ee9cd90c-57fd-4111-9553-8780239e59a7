import React, { useState } from 'react';
import { formatCurrency, formatDate } from '../utils/currency';

const TransportExpenses = () => {
  const [expenses, setExpenses] = useState([
    {
      id: 1,
      date: '2024-01-15',
      transportType: 'شحن بضائع',
      destination: 'طرابلس',
      amount: 3200,
      driverName: 'أحمد محمد علي',
      vehicleNumber: 'ط ر ب 1234',
      notes: 'شحن سريع - بضائع حساسة',
      distance: 450,
      fuelCost: 800,
      driverFee: 1200,
      otherExpenses: 1200
    },
    {
      id: 2,
      date: '2024-01-14',
      transportType: 'نقل موظفين',
      destination: 'بنغازي',
      amount: 2800,
      driverName: 'محمد أحمد سالم',
      vehicleNumber: 'ب ن غ 5678',
      notes: 'نقل فريق الصيانة',
      distance: 650,
      fuelCost: 1100,
      driverFee: 1000,
      otherExpenses: 700
    },
    {
      id: 3,
      date: '2024-01-13',
      transportType: 'توصيل مواد',
      destination: 'مصراتة',
      amount: 1800,
      driverName: 'سالم عبدالله',
      vehicleNumber: 'م ص ر 9012',
      notes: 'توصيل مواد خام',
      distance: 200,
      fuelCost: 400,
      driverFee: 800,
      otherExpenses: 600
    },
    {
      id: 4,
      date: '2024-01-12',
      transportType: 'شحن معدات',
      destination: 'سبها',
      amount: 4500,
      driverName: 'عبدالرحمن محمد',
      vehicleNumber: 'س ب ه 3456',
      notes: 'شحن معدات ثقيلة',
      distance: 750,
      fuelCost: 1500,
      driverFee: 1800,
      otherExpenses: 1200
    }
  ]);

  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    transportType: '',
    destination: '',
    amount: '',
    driverName: '',
    vehicleNumber: '',
    notes: '',
    distance: '',
    fuelCost: '',
    driverFee: '',
    otherExpenses: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    const newExpense = {
      id: expenses.length + 1,
      ...formData,
      amount: parseFloat(formData.amount) || 0,
      distance: parseInt(formData.distance) || 0,
      fuelCost: parseFloat(formData.fuelCost) || 0,
      driverFee: parseFloat(formData.driverFee) || 0,
      otherExpenses: parseFloat(formData.otherExpenses) || 0
    };

    setExpenses(prev => [newExpense, ...prev]);
    setFormData({
      date: new Date().toISOString().split('T')[0],
      transportType: '',
      destination: '',
      amount: '',
      driverName: '',
      vehicleNumber: '',
      notes: '',
      distance: '',
      fuelCost: '',
      driverFee: '',
      otherExpenses: ''
    });
    setShowForm(false);
  };

  const getTotalExpenses = () => {
    return expenses.reduce((sum, expense) => sum + expense.amount, 0);
  };

  return (
    <div className="transport-expenses">
      <div className="stats-grid" style={{ marginBottom: '30px' }}>
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#dc3545' }}>
            {formatCurrency(getTotalExpenses())}
          </div>
          <div className="stat-label">إجمالي مصروفات النقل</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#1e3a8a' }}>
            {expenses.length}
          </div>
          <div className="stat-label">عدد الرحلات</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#ffa500' }}>
            {expenses.reduce((sum, expense) => sum + expense.distance, 0)} كم
          </div>
          <div className="stat-label">إجمالي المسافة</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#ea580c' }}>
            {formatCurrency(expenses.reduce((sum, expense) => sum + expense.fuelCost, 0))}
          </div>
          <div className="stat-label">تكلفة الوقود</div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="card-title">مصروفات الحركة والنقل</h3>
          <button 
            className="btn btn-primary"
            onClick={() => setShowForm(!showForm)}
          >
            ➕ إضافة مصروف نقل
          </button>
        </div>

        {/* نموذج إضافة مصروف نقل */}
        {showForm && (
          <form onSubmit={handleSubmit} style={{ marginBottom: '20px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
            <div className="form-row">
              <div className="form-group">
                <label className="form-label">التاريخ</label>
                <input
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  className="form-input"
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">نوع النقل</label>
                <select
                  name="transportType"
                  value={formData.transportType}
                  onChange={handleInputChange}
                  className="form-input"
                  required
                >
                  <option value="">اختر نوع النقل</option>
                  <option value="شحن بضائع">شحن بضائع</option>
                  <option value="نقل موظفين">نقل موظفين</option>
                  <option value="توصيل مواد">توصيل مواد</option>
                  <option value="شحن معدات">شحن معدات</option>
                </select>
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label className="form-label">الوجهة</label>
                <input
                  type="text"
                  name="destination"
                  value={formData.destination}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="الوجهة"
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">المسافة (كم)</label>
                <input
                  type="number"
                  name="distance"
                  value={formData.distance}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="المسافة بالكيلومتر"
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label className="form-label">اسم السائق</label>
                <input
                  type="text"
                  name="driverName"
                  value={formData.driverName}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="اسم السائق"
                />
              </div>

              <div className="form-group">
                <label className="form-label">رقم المركبة</label>
                <input
                  type="text"
                  name="vehicleNumber"
                  value={formData.vehicleNumber}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="رقم المركبة"
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label className="form-label">تكلفة الوقود</label>
                <input
                  type="number"
                  name="fuelCost"
                  value={formData.fuelCost}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="تكلفة الوقود"
                  step="0.01"
                />
              </div>

              <div className="form-group">
                <label className="form-label">أجرة السائق</label>
                <input
                  type="number"
                  name="driverFee"
                  value={formData.driverFee}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="أجرة السائق"
                  step="0.01"
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label className="form-label">مصروفات أخرى</label>
                <input
                  type="number"
                  name="otherExpenses"
                  value={formData.otherExpenses}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="مصروفات أخرى"
                  step="0.01"
                />
              </div>

              <div className="form-group">
                <label className="form-label">إجمالي المبلغ</label>
                <input
                  type="number"
                  name="amount"
                  value={formData.amount}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="إجمالي المبلغ"
                  step="0.01"
                  required
                />
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">ملاحظات</label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                className="form-input"
                placeholder="ملاحظات إضافية"
                rows="3"
              />
            </div>

            <div style={{ display: 'flex', gap: '10px' }}>
              <button type="submit" className="btn btn-primary">
                💾 حفظ مصروف النقل
              </button>
              <button
                type="button"
                className="btn btn-secondary"
                onClick={() => setShowForm(false)}
              >
                ❌ إلغاء
              </button>
            </div>
          </form>
        )}

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>نوع النقل</th>
                <th>الوجهة</th>
                <th>المسافة</th>
                <th>تكلفة الوقود</th>
                <th>أجرة السائق</th>
                <th>إجمالي المبلغ</th>
                <th>اسم السائق</th>
                <th>رقم المركبة</th>
                <th>إجراءات</th>
              </tr>
            </thead>
            <tbody>
              {expenses.map((expense) => (
                <tr key={expense.id}>
                  <td>{formatDate(expense.date)}</td>
                  <td>{expense.transportType}</td>
                  <td>{expense.destination}</td>
                  <td>{expense.distance} كم</td>
                  <td style={{ color: '#ea580c' }}>{formatCurrency(expense.fuelCost)}</td>
                  <td style={{ color: '#1e40af' }}>{formatCurrency(expense.driverFee)}</td>
                  <td style={{ fontWeight: 'bold', color: '#dc3545' }}>
                    {formatCurrency(expense.amount)}
                  </td>
                  <td>{expense.driverName}</td>
                  <td>{expense.vehicleNumber}</td>
                  <td>
                    <button className="btn btn-secondary" style={{ padding: '5px 10px', fontSize: '12px' }}>
                      ✏️ تعديل
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TransportExpenses;
