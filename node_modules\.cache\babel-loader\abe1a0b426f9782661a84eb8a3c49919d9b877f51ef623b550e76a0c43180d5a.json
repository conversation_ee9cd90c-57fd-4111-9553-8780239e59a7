{"ast": null, "code": "// دالة تنسيق العملة بالدينار الليبي\nexport const formatCurrency = amount => {\n  if (amount === null || amount === undefined || isNaN(amount)) {\n    return '0.00 د.ل';\n  }\n  return new Intl.NumberFormat('ar-LY', {\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  }).format(amount) + ' د.ل';\n};\n\n// دالة تحويل النص إلى رقم\nexport const parseAmount = value => {\n  if (!value) return 0;\n  const numericValue = parseFloat(value.toString().replace(/[^\\d.-]/g, ''));\n  return isNaN(numericValue) ? 0 : numericValue;\n};\n\n// دالة تنسيق الأرقام بدون رمز العملة\nexport const formatNumber = amount => {\n  if (amount === null || amount === undefined || isNaN(amount)) {\n    return '0';\n  }\n  return new Intl.NumberFormat('ar-LY').format(amount);\n};", "map": {"version": 3, "names": ["formatCurrency", "amount", "undefined", "isNaN", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "parseAmount", "value", "numericValue", "parseFloat", "toString", "replace", "formatNumber"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/utils/currency.js"], "sourcesContent": ["// دالة تنسيق العملة بالدينار الليبي\nexport const formatCurrency = (amount) => {\n  if (amount === null || amount === undefined || isNaN(amount)) {\n    return '0.00 د.ل';\n  }\n  \n  return new Intl.NumberFormat('ar-LY', {\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  }).format(amount) + ' د.ل';\n};\n\n// دالة تحويل النص إلى رقم\nexport const parseAmount = (value) => {\n  if (!value) return 0;\n  const numericValue = parseFloat(value.toString().replace(/[^\\d.-]/g, ''));\n  return isNaN(numericValue) ? 0 : numericValue;\n};\n\n// دالة تنسيق الأرقام بدون رمز العملة\nexport const formatNumber = (amount) => {\n  if (amount === null || amount === undefined || isNaN(amount)) {\n    return '0';\n  }\n  \n  return new Intl.NumberFormat('ar-LY').format(amount);\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,cAAc,GAAIC,MAAM,IAAK;EACxC,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKC,SAAS,IAAIC,KAAK,CAACF,MAAM,CAAC,EAAE;IAC5D,OAAO,UAAU;EACnB;EAEA,OAAO,IAAIG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IACpCC,qBAAqB,EAAE,CAAC;IACxBC,qBAAqB,EAAE;EACzB,CAAC,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC,GAAG,MAAM;AAC5B,CAAC;;AAED;AACA,OAAO,MAAMQ,WAAW,GAAIC,KAAK,IAAK;EACpC,IAAI,CAACA,KAAK,EAAE,OAAO,CAAC;EACpB,MAAMC,YAAY,GAAGC,UAAU,CAACF,KAAK,CAACG,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;EACzE,OAAOX,KAAK,CAACQ,YAAY,CAAC,GAAG,CAAC,GAAGA,YAAY;AAC/C,CAAC;;AAED;AACA,OAAO,MAAMI,YAAY,GAAId,MAAM,IAAK;EACtC,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKC,SAAS,IAAIC,KAAK,CAACF,MAAM,CAAC,EAAE;IAC5D,OAAO,GAAG;EACZ;EAEA,OAAO,IAAIG,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACG,MAAM,CAACP,MAAM,CAAC;AACtD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}