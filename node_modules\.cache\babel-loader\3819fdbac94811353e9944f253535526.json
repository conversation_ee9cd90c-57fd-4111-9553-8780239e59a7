{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/React.createElement(React.StrictMode, {\n  __self: this,\n  __source: {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 3\n  }\n}, /*#__PURE__*/React.createElement(App, {\n  __self: this,\n  __source: {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }\n})));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "root", "createRoot", "document", "getElementById", "render", "createElement", "StrictMode", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,GAAG,MAAM,OAAO;AAEvB,MAAMC,IAAI,GAAGF,QAAQ,CAACG,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cACTP,KAAA,CAAAQ,aAAA,CAACR,KAAK,CAACS,UAAU;EAAAC,MAAA;EAAAC,QAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA;AAAA,gBACff,KAAA,CAAAQ,aAAA,CAACN,GAAG;EAAAQ,MAAA;EAAAC,QAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA;AAAA,CAAE,CACU,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}