{"ast": null, "code": "/**\n * When source maps are enabled, `style-loader` uses a link element with a data-uri to\n * embed the css on the page. This breaks all relative urls because now they are relative to a\n * bundle instead of the current page.\n *\n * One solution is to only use full urls, but that may be impossible.\n *\n * Instead, this function \"fixes\" the relative urls to be absolute according to the current page location.\n *\n * A rudimentary test suite is located at `test/fixUrls.js` and can be run via the `npm test` command.\n *\n */\n\nmodule.exports = function (css) {\n  // get current location\n  var location = typeof window !== \"undefined\" && window.location;\n  if (!location) {\n    throw new Error(\"fixUrls requires window.location\");\n  }\n\n  // blank or null?\n  if (!css || typeof css !== \"string\") {\n    return css;\n  }\n  var baseUrl = location.protocol + \"//\" + location.host;\n  var currentDir = baseUrl + location.pathname.replace(/\\/[^\\/]*$/, \"/\");\n\n  // convert each url(...)\n  /*\n  This regular expression is just a way to recursively match brackets within\n  a string.\n  \t /url\\s*\\(  = Match on the word \"url\" with any whitespace after it and then a parens\n     (  = Start a capturing group\n       (?:  = Start a non-capturing group\n           [^)(]  = Match anything that isn't a parentheses\n           |  = OR\n           \\(  = Match a start parentheses\n               (?:  = Start another non-capturing groups\n                   [^)(]+  = Match anything that isn't a parentheses\n                   |  = OR\n                   \\(  = Match a start parentheses\n                       [^)(]*  = Match anything that isn't a parentheses\n                   \\)  = Match a end parentheses\n               )  = End Group\n               *\\) = Match anything and then a close parens\n           )  = Close non-capturing group\n           *  = Match anything\n        )  = Close capturing group\n   \\)  = Match a close parens\n  \t /gi  = Get all matches, not the first.  Be case insensitive.\n   */\n  var fixedCss = css.replace(/url\\s*\\(((?:[^)(]|\\((?:[^)(]+|\\([^)(]*\\))*\\))*)\\)/gi, function (fullMatch, origUrl) {\n    // strip quotes (if they exist)\n    var unquotedOrigUrl = origUrl.trim().replace(/^\"(.*)\"$/, function (o, $1) {\n      return $1;\n    }).replace(/^'(.*)'$/, function (o, $1) {\n      return $1;\n    });\n\n    // already a full url? no change\n    if (/^(#|data:|http:\\/\\/|https:\\/\\/|file:\\/\\/\\/|\\s*$)/i.test(unquotedOrigUrl)) {\n      return fullMatch;\n    }\n\n    // convert the url to a full url\n    var newUrl;\n    if (unquotedOrigUrl.indexOf(\"//\") === 0) {\n      //TODO: should we add protocol?\n      newUrl = unquotedOrigUrl;\n    } else if (unquotedOrigUrl.indexOf(\"/\") === 0) {\n      // path should be relative to the base url\n      newUrl = baseUrl + unquotedOrigUrl; // already starts with '/'\n    } else {\n      // path should be relative to current directory\n      newUrl = currentDir + unquotedOrigUrl.replace(/^\\.\\//, \"\"); // Strip leading './'\n    }\n\n    // send back the fixed url(...)\n    return \"url(\" + JSON.stringify(newUrl) + \")\";\n  });\n\n  // send back the fixed css\n  return fixedCss;\n};", "map": {"version": 3, "names": ["module", "exports", "css", "location", "window", "Error", "baseUrl", "protocol", "host", "currentDir", "pathname", "replace", "fixedCss", "fullMatch", "origUrl", "unquotedOrigUrl", "trim", "o", "$1", "test", "newUrl", "indexOf", "JSON", "stringify"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/node_modules/style-loader/lib/urls.js"], "sourcesContent": ["\n/**\n * When source maps are enabled, `style-loader` uses a link element with a data-uri to\n * embed the css on the page. This breaks all relative urls because now they are relative to a\n * bundle instead of the current page.\n *\n * One solution is to only use full urls, but that may be impossible.\n *\n * Instead, this function \"fixes\" the relative urls to be absolute according to the current page location.\n *\n * A rudimentary test suite is located at `test/fixUrls.js` and can be run via the `npm test` command.\n *\n */\n\nmodule.exports = function (css) {\n  // get current location\n  var location = typeof window !== \"undefined\" && window.location;\n\n  if (!location) {\n    throw new Error(\"fixUrls requires window.location\");\n  }\n\n\t// blank or null?\n\tif (!css || typeof css !== \"string\") {\n\t  return css;\n  }\n\n  var baseUrl = location.protocol + \"//\" + location.host;\n  var currentDir = baseUrl + location.pathname.replace(/\\/[^\\/]*$/, \"/\");\n\n\t// convert each url(...)\n\t/*\n\tThis regular expression is just a way to recursively match brackets within\n\ta string.\n\n\t /url\\s*\\(  = Match on the word \"url\" with any whitespace after it and then a parens\n\t   (  = Start a capturing group\n\t     (?:  = Start a non-capturing group\n\t         [^)(]  = Match anything that isn't a parentheses\n\t         |  = OR\n\t         \\(  = Match a start parentheses\n\t             (?:  = Start another non-capturing groups\n\t                 [^)(]+  = Match anything that isn't a parentheses\n\t                 |  = OR\n\t                 \\(  = Match a start parentheses\n\t                     [^)(]*  = Match anything that isn't a parentheses\n\t                 \\)  = Match a end parentheses\n\t             )  = End Group\n              *\\) = Match anything and then a close parens\n          )  = Close non-capturing group\n          *  = Match anything\n       )  = Close capturing group\n\t \\)  = Match a close parens\n\n\t /gi  = Get all matches, not the first.  Be case insensitive.\n\t */\n\tvar fixedCss = css.replace(/url\\s*\\(((?:[^)(]|\\((?:[^)(]+|\\([^)(]*\\))*\\))*)\\)/gi, function(fullMatch, origUrl) {\n\t\t// strip quotes (if they exist)\n\t\tvar unquotedOrigUrl = origUrl\n\t\t\t.trim()\n\t\t\t.replace(/^\"(.*)\"$/, function(o, $1){ return $1; })\n\t\t\t.replace(/^'(.*)'$/, function(o, $1){ return $1; });\n\n\t\t// already a full url? no change\n\t\tif (/^(#|data:|http:\\/\\/|https:\\/\\/|file:\\/\\/\\/|\\s*$)/i.test(unquotedOrigUrl)) {\n\t\t  return fullMatch;\n\t\t}\n\n\t\t// convert the url to a full url\n\t\tvar newUrl;\n\n\t\tif (unquotedOrigUrl.indexOf(\"//\") === 0) {\n\t\t  \t//TODO: should we add protocol?\n\t\t\tnewUrl = unquotedOrigUrl;\n\t\t} else if (unquotedOrigUrl.indexOf(\"/\") === 0) {\n\t\t\t// path should be relative to the base url\n\t\t\tnewUrl = baseUrl + unquotedOrigUrl; // already starts with '/'\n\t\t} else {\n\t\t\t// path should be relative to current directory\n\t\t\tnewUrl = currentDir + unquotedOrigUrl.replace(/^\\.\\//, \"\"); // Strip leading './'\n\t\t}\n\n\t\t// send back the fixed url(...)\n\t\treturn \"url(\" + JSON.stringify(newUrl) + \")\";\n\t});\n\n\t// send back the fixed css\n\treturn fixedCss;\n};\n"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAA,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAE;EAC9B;EACA,IAAIC,QAAQ,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACD,QAAQ;EAE/D,IAAI,CAACA,QAAQ,EAAE;IACb,MAAM,IAAIE,KAAK,CAAC,kCAAkC,CAAC;EACrD;;EAED;EACA,IAAI,CAACH,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACnC,OAAOA,GAAG;EACX;EAEA,IAAII,OAAO,GAAGH,QAAQ,CAACI,QAAQ,GAAG,IAAI,GAAGJ,QAAQ,CAACK,IAAI;EACtD,IAAIC,UAAU,GAAGH,OAAO,GAAGH,QAAQ,CAACO,QAAQ,CAACC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC;;EAEvE;EACA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAGC,IAAIC,QAAQ,GAAGV,GAAG,CAACS,OAAO,CAAC,qDAAqD,EAAE,UAASE,SAAS,EAAEC,OAAO,EAAE;IAC9G;IACA,IAAIC,eAAe,GAAGD,OAAO,CAC3BE,IAAI,CAAC,CAAC,CACNL,OAAO,CAAC,UAAU,EAAE,UAASM,CAAC,EAAEC,EAAE,EAAC;MAAE,OAAOA,EAAE;IAAE,CAAC,CAAC,CAClDP,OAAO,CAAC,UAAU,EAAE,UAASM,CAAC,EAAEC,EAAE,EAAC;MAAE,OAAOA,EAAE;IAAE,CAAC,CAAC;;IAEpD;IACA,IAAI,mDAAmD,CAACC,IAAI,CAACJ,eAAe,CAAC,EAAE;MAC7E,OAAOF,SAAS;IAClB;;IAEA;IACA,IAAIO,MAAM;IAEV,IAAIL,eAAe,CAACM,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;MACtC;MACFD,MAAM,GAAGL,eAAe;IACzB,CAAC,MAAM,IAAIA,eAAe,CAACM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;MAC9C;MACAD,MAAM,GAAGd,OAAO,GAAGS,eAAe,CAAC,CAAC;IACrC,CAAC,MAAM;MACN;MACAK,MAAM,GAAGX,UAAU,GAAGM,eAAe,CAACJ,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IAC7D;;IAEA;IACA,OAAO,MAAM,GAAGW,IAAI,CAACC,SAAS,CAACH,MAAM,CAAC,GAAG,GAAG;EAC7C,CAAC,CAAC;;EAEF;EACA,OAAOR,QAAQ;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}