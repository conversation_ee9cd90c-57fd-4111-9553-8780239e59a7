{"ast": null, "code": "var conversions = require('./conversions');\n\n/*\n\tthis function routes a model to all other models.\n\n\tall functions that are routed have a property `.conversion` attached\n\tto the returned synthetic function. This property is an array\n\tof strings, each with the steps in between the 'from' and 'to'\n\tcolor models (inclusive).\n\n\tconversions that are not possible simply are not included.\n*/\n\nfunction buildGraph() {\n  var graph = {};\n  // https://jsperf.com/object-keys-vs-for-in-with-closure/3\n  var models = Object.keys(conversions);\n  for (var len = models.length, i = 0; i < len; i++) {\n    graph[models[i]] = {\n      // http://jsperf.com/1-vs-infinity\n      // micro-opt, but this is simple.\n      distance: -1,\n      parent: null\n    };\n  }\n  return graph;\n}\n\n// https://en.wikipedia.org/wiki/Breadth-first_search\nfunction deriveBFS(fromModel) {\n  var graph = buildGraph();\n  var queue = [fromModel]; // unshift -> queue -> pop\n\n  graph[fromModel].distance = 0;\n  while (queue.length) {\n    var current = queue.pop();\n    var adjacents = Object.keys(conversions[current]);\n    for (var len = adjacents.length, i = 0; i < len; i++) {\n      var adjacent = adjacents[i];\n      var node = graph[adjacent];\n      if (node.distance === -1) {\n        node.distance = graph[current].distance + 1;\n        node.parent = current;\n        queue.unshift(adjacent);\n      }\n    }\n  }\n  return graph;\n}\nfunction link(from, to) {\n  return function (args) {\n    return to(from(args));\n  };\n}\nfunction wrapConversion(toModel, graph) {\n  var path = [graph[toModel].parent, toModel];\n  var fn = conversions[graph[toModel].parent][toModel];\n  var cur = graph[toModel].parent;\n  while (graph[cur].parent) {\n    path.unshift(graph[cur].parent);\n    fn = link(conversions[graph[cur].parent][cur], fn);\n    cur = graph[cur].parent;\n  }\n  fn.conversion = path;\n  return fn;\n}\nmodule.exports = function (fromModel) {\n  var graph = deriveBFS(fromModel);\n  var conversion = {};\n  var models = Object.keys(graph);\n  for (var len = models.length, i = 0; i < len; i++) {\n    var toModel = models[i];\n    var node = graph[toModel];\n    if (node.parent === null) {\n      // no possible conversion, or this node is the source model.\n      continue;\n    }\n    conversion[toModel] = wrapConversion(toModel, graph);\n  }\n  return conversion;\n};", "map": {"version": 3, "names": ["conversions", "require", "buildGraph", "graph", "models", "Object", "keys", "len", "length", "i", "distance", "parent", "deriveBFS", "fromModel", "queue", "current", "pop", "adjacents", "adjacent", "node", "unshift", "link", "from", "to", "args", "wrapConversion", "toModel", "path", "fn", "cur", "conversion", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/node_modules/react-dev-utils/node_modules/color-convert/route.js"], "sourcesContent": ["var conversions = require('./conversions');\n\n/*\n\tthis function routes a model to all other models.\n\n\tall functions that are routed have a property `.conversion` attached\n\tto the returned synthetic function. This property is an array\n\tof strings, each with the steps in between the 'from' and 'to'\n\tcolor models (inclusive).\n\n\tconversions that are not possible simply are not included.\n*/\n\nfunction buildGraph() {\n\tvar graph = {};\n\t// https://jsperf.com/object-keys-vs-for-in-with-closure/3\n\tvar models = Object.keys(conversions);\n\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tgraph[models[i]] = {\n\t\t\t// http://jsperf.com/1-vs-infinity\n\t\t\t// micro-opt, but this is simple.\n\t\t\tdistance: -1,\n\t\t\tparent: null\n\t\t};\n\t}\n\n\treturn graph;\n}\n\n// https://en.wikipedia.org/wiki/Breadth-first_search\nfunction deriveBFS(fromModel) {\n\tvar graph = buildGraph();\n\tvar queue = [fromModel]; // unshift -> queue -> pop\n\n\tgraph[fromModel].distance = 0;\n\n\twhile (queue.length) {\n\t\tvar current = queue.pop();\n\t\tvar adjacents = Object.keys(conversions[current]);\n\n\t\tfor (var len = adjacents.length, i = 0; i < len; i++) {\n\t\t\tvar adjacent = adjacents[i];\n\t\t\tvar node = graph[adjacent];\n\n\t\t\tif (node.distance === -1) {\n\t\t\t\tnode.distance = graph[current].distance + 1;\n\t\t\t\tnode.parent = current;\n\t\t\t\tqueue.unshift(adjacent);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn graph;\n}\n\nfunction link(from, to) {\n\treturn function (args) {\n\t\treturn to(from(args));\n\t};\n}\n\nfunction wrapConversion(toModel, graph) {\n\tvar path = [graph[toModel].parent, toModel];\n\tvar fn = conversions[graph[toModel].parent][toModel];\n\n\tvar cur = graph[toModel].parent;\n\twhile (graph[cur].parent) {\n\t\tpath.unshift(graph[cur].parent);\n\t\tfn = link(conversions[graph[cur].parent][cur], fn);\n\t\tcur = graph[cur].parent;\n\t}\n\n\tfn.conversion = path;\n\treturn fn;\n}\n\nmodule.exports = function (fromModel) {\n\tvar graph = deriveBFS(fromModel);\n\tvar conversion = {};\n\n\tvar models = Object.keys(graph);\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tvar toModel = models[i];\n\t\tvar node = graph[toModel];\n\n\t\tif (node.parent === null) {\n\t\t\t// no possible conversion, or this node is the source model.\n\t\t\tcontinue;\n\t\t}\n\n\t\tconversion[toModel] = wrapConversion(toModel, graph);\n\t}\n\n\treturn conversion;\n};\n\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,UAAUA,CAAA,EAAG;EACrB,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACN,WAAW,CAAC;EAErC,KAAK,IAAIO,GAAG,GAAGH,MAAM,CAACI,MAAM,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IAClDN,KAAK,CAACC,MAAM,CAACK,CAAC,CAAC,CAAC,GAAG;MAClB;MACA;MACAC,QAAQ,EAAE,CAAC,CAAC;MACZC,MAAM,EAAE;IACT,CAAC;EACF;EAEA,OAAOR,KAAK;AACb;;AAEA;AACA,SAASS,SAASA,CAACC,SAAS,EAAE;EAC7B,IAAIV,KAAK,GAAGD,UAAU,CAAC,CAAC;EACxB,IAAIY,KAAK,GAAG,CAACD,SAAS,CAAC,CAAC,CAAC;;EAEzBV,KAAK,CAACU,SAAS,CAAC,CAACH,QAAQ,GAAG,CAAC;EAE7B,OAAOI,KAAK,CAACN,MAAM,EAAE;IACpB,IAAIO,OAAO,GAAGD,KAAK,CAACE,GAAG,CAAC,CAAC;IACzB,IAAIC,SAAS,GAAGZ,MAAM,CAACC,IAAI,CAACN,WAAW,CAACe,OAAO,CAAC,CAAC;IAEjD,KAAK,IAAIR,GAAG,GAAGU,SAAS,CAACT,MAAM,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MACrD,IAAIS,QAAQ,GAAGD,SAAS,CAACR,CAAC,CAAC;MAC3B,IAAIU,IAAI,GAAGhB,KAAK,CAACe,QAAQ,CAAC;MAE1B,IAAIC,IAAI,CAACT,QAAQ,KAAK,CAAC,CAAC,EAAE;QACzBS,IAAI,CAACT,QAAQ,GAAGP,KAAK,CAACY,OAAO,CAAC,CAACL,QAAQ,GAAG,CAAC;QAC3CS,IAAI,CAACR,MAAM,GAAGI,OAAO;QACrBD,KAAK,CAACM,OAAO,CAACF,QAAQ,CAAC;MACxB;IACD;EACD;EAEA,OAAOf,KAAK;AACb;AAEA,SAASkB,IAAIA,CAACC,IAAI,EAAEC,EAAE,EAAE;EACvB,OAAO,UAAUC,IAAI,EAAE;IACtB,OAAOD,EAAE,CAACD,IAAI,CAACE,IAAI,CAAC,CAAC;EACtB,CAAC;AACF;AAEA,SAASC,cAAcA,CAACC,OAAO,EAAEvB,KAAK,EAAE;EACvC,IAAIwB,IAAI,GAAG,CAACxB,KAAK,CAACuB,OAAO,CAAC,CAACf,MAAM,EAAEe,OAAO,CAAC;EAC3C,IAAIE,EAAE,GAAG5B,WAAW,CAACG,KAAK,CAACuB,OAAO,CAAC,CAACf,MAAM,CAAC,CAACe,OAAO,CAAC;EAEpD,IAAIG,GAAG,GAAG1B,KAAK,CAACuB,OAAO,CAAC,CAACf,MAAM;EAC/B,OAAOR,KAAK,CAAC0B,GAAG,CAAC,CAAClB,MAAM,EAAE;IACzBgB,IAAI,CAACP,OAAO,CAACjB,KAAK,CAAC0B,GAAG,CAAC,CAAClB,MAAM,CAAC;IAC/BiB,EAAE,GAAGP,IAAI,CAACrB,WAAW,CAACG,KAAK,CAAC0B,GAAG,CAAC,CAAClB,MAAM,CAAC,CAACkB,GAAG,CAAC,EAAED,EAAE,CAAC;IAClDC,GAAG,GAAG1B,KAAK,CAAC0B,GAAG,CAAC,CAAClB,MAAM;EACxB;EAEAiB,EAAE,CAACE,UAAU,GAAGH,IAAI;EACpB,OAAOC,EAAE;AACV;AAEAG,MAAM,CAACC,OAAO,GAAG,UAAUnB,SAAS,EAAE;EACrC,IAAIV,KAAK,GAAGS,SAAS,CAACC,SAAS,CAAC;EAChC,IAAIiB,UAAU,GAAG,CAAC,CAAC;EAEnB,IAAI1B,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC;EAC/B,KAAK,IAAII,GAAG,GAAGH,MAAM,CAACI,MAAM,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IAClD,IAAIiB,OAAO,GAAGtB,MAAM,CAACK,CAAC,CAAC;IACvB,IAAIU,IAAI,GAAGhB,KAAK,CAACuB,OAAO,CAAC;IAEzB,IAAIP,IAAI,CAACR,MAAM,KAAK,IAAI,EAAE;MACzB;MACA;IACD;IAEAmB,UAAU,CAACJ,OAAO,CAAC,GAAGD,cAAc,CAACC,OAAO,EAAEvB,KAAK,CAAC;EACrD;EAEA,OAAO2B,UAAU;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}