{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\Dashboard.js\";\nimport React, { useState, useEffect } from 'react';\nimport { formatCurrency } from '../utils/currency';\nconst Dashboard = () => {\n  const currentMonth = 6;\n\n  // بيانات مطابقة للنموذج\n  const [monthlyData, setMonthlyData] = useState({\n    treasuryDeposits: 40180.50,\n    employeeWithdrawals: 2500.00,\n    factoryExpenses: 0,\n    livingExpenses: 0,\n    paintExpenses: 0,\n    transportExpenses: 26.00,\n    warehousePurchases: 0,\n    treasuryBalance: 37654.50\n  });\n  useEffect(() => {\n    // حساب رصيد الخزينة\n    const balance = monthlyData.treasuryDeposits - (monthlyData.employeeWithdrawals + monthlyData.factoryExpenses + monthlyData.livingExpenses + monthlyData.paintExpenses + monthlyData.transportExpenses + monthlyData.warehousePurchases);\n    setMonthlyData(prev => ({\n      ...prev,\n      treasuryBalance: balance\n    }));\n  }, []);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"dashboard\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card\",\n    style: {\n      marginBottom: '30px',\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: {\n      color: '#ffa500',\n      fontSize: '24px',\n      fontWeight: 'bold',\n      margin: '20px 0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 9\n    }\n  }, currentMonth, \" - \\u0643\\u0634\\u0641 \\u062D\\u0633\\u0627\\u0628 \\u0634\\u0647\\u0631\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"monthly-statement-table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    style: {\n      width: '100%',\n      borderCollapse: 'collapse',\n      fontSize: '16px',\n      fontWeight: 'bold'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    style: {\n      backgroundColor: '#1e3a8a',\n      color: 'white'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 17\n    }\n  }, \"\\u0625\\u064A\\u062F\\u0627\\u0639\\u0627\\u062A \\u0627\\u0644\\u062E\\u0632\\u064A\\u0646\\u0629\"), /*#__PURE__*/React.createElement(\"th\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 17\n    }\n  }, \"\\u0645\\u0633\\u062D\\u0648\\u0628\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0638\\u0641\\u064A\\u0646\"), /*#__PURE__*/React.createElement(\"th\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 17\n    }\n  }, \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0645\\u0635\\u0646\\u0639\"), /*#__PURE__*/React.createElement(\"th\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 17\n    }\n  }, \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0625\\u0628\\u0639\\u0627\\u0626\\u064A\\u0629\"), /*#__PURE__*/React.createElement(\"th\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 17\n    }\n  }, \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0637\\u0644\\u0627\\u0621\"), /*#__PURE__*/React.createElement(\"th\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 17\n    }\n  }, \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u062D\\u0631\\u0643\\u0629 \\u0648\\u0627\\u0644\\u0646\\u0642\\u0644\"), /*#__PURE__*/React.createElement(\"th\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 17\n    }\n  }, \"\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0646\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    style: {\n      backgroundColor: '#f8f9fa'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd',\n      color: '#ffa500',\n      fontWeight: 'bold'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 17\n    }\n  }, formatCurrency(monthlyData.treasuryDeposits)), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd',\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 17\n    }\n  }, monthlyData.employeeWithdrawals > 0 ? formatCurrency(monthlyData.employeeWithdrawals) : '-'), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd',\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 17\n    }\n  }, monthlyData.factoryExpenses > 0 ? formatCurrency(monthlyData.factoryExpenses) : '-'), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd',\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 17\n    }\n  }, monthlyData.livingExpenses > 0 ? formatCurrency(monthlyData.livingExpenses) : '-'), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd',\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 17\n    }\n  }, monthlyData.paintExpenses > 0 ? formatCurrency(monthlyData.paintExpenses) : '-'), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd',\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 17\n    }\n  }, monthlyData.transportExpenses > 0 ? formatCurrency(monthlyData.transportExpenses) : '-'), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd',\n      color: '#dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 17\n    }\n  }, monthlyData.warehousePurchases > 0 ? formatCurrency(monthlyData.warehousePurchases) : '-'))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'center',\n      marginTop: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      alignItems: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      backgroundColor: '#dc3545',\n      color: 'white',\n      padding: '15px 30px',\n      fontSize: '20px',\n      fontWeight: 'bold',\n      borderRadius: '5px 0 0 5px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 13\n    }\n  }, formatCurrency(monthlyData.treasuryBalance)), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      backgroundColor: '#1e3a8a',\n      color: 'white',\n      padding: '15px 30px',\n      fontSize: '20px',\n      fontWeight: 'bold',\n      borderRadius: '0 5px 5px 0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 13\n    }\n  }, \"\\u0627\\u0644\\u062E\\u0632\\u064A\\u0646\\u0629\")))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',\n      gap: '20px',\n      marginTop: '30px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(DetailedSection, {\n    title: \"\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0646\",\n    total: monthlyData.warehousePurchases,\n    items: [],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 9\n    }\n  }), /*#__PURE__*/React.createElement(DetailedSection, {\n    title: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u062D\\u0631\\u0643\\u0629 \\u0648\\u0627\\u0644\\u0646\\u0642\\u0644\",\n    total: monthlyData.transportExpenses,\n    items: [{\n      description: 'نقل مرون الحلاج',\n      amount: 26.00,\n      date: '06/01/2025'\n    }],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 9\n    }\n  }), /*#__PURE__*/React.createElement(DetailedSection, {\n    title: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0625\\u0628\\u0639\\u0627\\u0626\\u064A\\u0629\",\n    total: monthlyData.livingExpenses,\n    items: [],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 9\n    }\n  }), /*#__PURE__*/React.createElement(DetailedSection, {\n    title: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0637\\u0644\\u0627\\u0621\",\n    total: monthlyData.paintExpenses,\n    items: [],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 9\n    }\n  }), /*#__PURE__*/React.createElement(DetailedSection, {\n    title: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0645\\u0635\\u0646\\u0639\",\n    total: monthlyData.factoryExpenses,\n    items: [],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 9\n    }\n  }), /*#__PURE__*/React.createElement(DetailedSection, {\n    title: \"\\u0645\\u0633\\u062D\\u0648\\u0628\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0638\\u0641\\u064A\\u0646\",\n    total: monthlyData.employeeWithdrawals,\n    items: [],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 9\n    }\n  }), /*#__PURE__*/React.createElement(DetailedSection, {\n    title: \"\\u0625\\u064A\\u062F\\u0627\\u0639\\u0627\\u062A \\u0627\\u0644\\u062E\\u0632\\u064A\\u0646\\u0629\",\n    total: monthlyData.treasuryDeposits,\n    items: [{\n      description: 'رصيد أول المدة',\n      amount: 12180.5,\n      date: '06/01/2025'\n    }, {\n      description: 'المهندس ناصر أثاث المقة',\n      amount: 28000.0,\n      date: '06/01/2025'\n    }],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }\n  })));\n};\n\n// مكون القسم المفصل\nconst DetailedSection = ({\n  title,\n  total,\n  items\n}) => {\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"card\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      backgroundColor: '#6c757d',\n      color: 'white',\n      padding: '10px',\n      textAlign: 'center',\n      fontWeight: 'bold',\n      fontSize: '16px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }\n  }, title), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      backgroundColor: '#dc3545',\n      color: 'white',\n      padding: '10px',\n      textAlign: 'center',\n      fontWeight: 'bold',\n      fontSize: '18px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }\n  }, total > 0 ? formatCurrency(total) : '0', \" \\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0635\\u0627\\u0631\\u064A\\u0641\"), /*#__PURE__*/React.createElement(\"table\", {\n    style: {\n      width: '100%',\n      borderCollapse: 'collapse',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    style: {\n      backgroundColor: '#f8f9fa'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    style: {\n      padding: '10px',\n      textAlign: 'center',\n      border: '1px solid #ddd',\n      backgroundColor: '#ffa500',\n      color: 'white'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 13\n    }\n  }, \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"), /*#__PURE__*/React.createElement(\"th\", {\n    style: {\n      padding: '10px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 13\n    }\n  }, \"\\u0627\\u0644\\u0648\\u0635\\u0641\"), /*#__PURE__*/React.createElement(\"th\", {\n    style: {\n      padding: '10px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 13\n    }\n  }, \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 9\n    }\n  }, items.length > 0 ? items.map((item, index) => /*#__PURE__*/React.createElement(\"tr\", {\n    key: index,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      padding: '8px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 15\n    }\n  }, formatCurrency(item.amount)), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      padding: '8px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 15\n    }\n  }, item.description), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      padding: '8px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 15\n    }\n  }, item.date))) :\n  // صفوف فارغة للعرض\n  Array.from({\n    length: 5\n  }, (_, index) => /*#__PURE__*/React.createElement(\"tr\", {\n    key: index,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"td\", {\n    style: {\n      padding: '15px',\n      textAlign: 'center',\n      border: '1px solid #ddd'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 17\n    }\n  }))))));\n};\nexport default Dashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "formatCurrency", "Dashboard", "currentMonth", "monthlyData", "setMonthlyData", "treasuryDeposits", "employee<PERSON><PERSON><PERSON><PERSON>s", "factoryExpenses", "livingExpenses", "paintExpenses", "transportExpenses", "warehousePurchases", "treasuryBalance", "balance", "prev", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginBottom", "textAlign", "color", "fontSize", "fontWeight", "margin", "width", "borderCollapse", "backgroundColor", "padding", "border", "display", "justifyContent", "marginTop", "alignItems", "borderRadius", "gridTemplateColumns", "gap", "DetailedSection", "title", "total", "items", "description", "amount", "date", "length", "map", "item", "index", "key", "Array", "from", "_"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { formatCurrency } from '../utils/currency';\n\nconst Dashboard = () => {\n  const currentMonth = 6;\n\n  // بيانات مطابقة للنموذج\n  const [monthlyData, setMonthlyData] = useState({\n    treasuryDeposits: 40180.50,\n    employeeWithdrawals: 2500.00,\n    factoryExpenses: 0,\n    livingExpenses: 0,\n    paintExpenses: 0,\n    transportExpenses: 26.00,\n    warehousePurchases: 0,\n    treasuryBalance: 37654.50\n  });\n\n  useEffect(() => {\n    // حساب رصيد الخزينة\n    const balance = monthlyData.treasuryDeposits -\n                   (monthlyData.employeeWithdrawals +\n                    monthlyData.factoryExpenses +\n                    monthlyData.livingExpenses +\n                    monthlyData.paintExpenses +\n                    monthlyData.transportExpenses +\n                    monthlyData.warehousePurchases);\n\n    setMonthlyData(prev => ({\n      ...prev,\n      treasuryBalance: balance\n    }));\n  }, []);\n\n\n\n  return (\n    <div className=\"dashboard\">\n      {/* عنوان كشف الحساب الشهري */}\n      <div className=\"card\" style={{ marginBottom: '30px', textAlign: 'center' }}>\n        <h2 style={{\n          color: '#ffa500',\n          fontSize: '24px',\n          fontWeight: 'bold',\n          margin: '20px 0'\n        }}>\n          {currentMonth} - كشف حساب شهر\n        </h2>\n      </div>\n\n      {/* جدول كشف الحساب الرئيسي */}\n      <div className=\"card\">\n        <div className=\"monthly-statement-table\">\n          <table style={{\n            width: '100%',\n            borderCollapse: 'collapse',\n            fontSize: '16px',\n            fontWeight: 'bold'\n          }}>\n            <thead>\n              <tr style={{ backgroundColor: '#1e3a8a', color: 'white' }}>\n                <th style={{ padding: '15px', textAlign: 'center', border: '1px solid #ddd' }}>إيداعات الخزينة</th>\n                <th style={{ padding: '15px', textAlign: 'center', border: '1px solid #ddd' }}>مسحوبات الموظفين</th>\n                <th style={{ padding: '15px', textAlign: 'center', border: '1px solid #ddd' }}>مصروفات المصنع</th>\n                <th style={{ padding: '15px', textAlign: 'center', border: '1px solid #ddd' }}>مصروفات إبعائية</th>\n                <th style={{ padding: '15px', textAlign: 'center', border: '1px solid #ddd' }}>مصروفات الطلاء</th>\n                <th style={{ padding: '15px', textAlign: 'center', border: '1px solid #ddd' }}>مصروفات الحركة والنقل</th>\n                <th style={{ padding: '15px', textAlign: 'center', border: '1px solid #ddd' }}>مشتريات المخزن</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr style={{ backgroundColor: '#f8f9fa' }}>\n                <td style={{\n                  padding: '15px',\n                  textAlign: 'center',\n                  border: '1px solid #ddd',\n                  color: '#ffa500',\n                  fontWeight: 'bold'\n                }}>\n                  {formatCurrency(monthlyData.treasuryDeposits)}\n                </td>\n                <td style={{\n                  padding: '15px',\n                  textAlign: 'center',\n                  border: '1px solid #ddd',\n                  color: '#dc3545'\n                }}>\n                  {monthlyData.employeeWithdrawals > 0 ? formatCurrency(monthlyData.employeeWithdrawals) : '-'}\n                </td>\n                <td style={{\n                  padding: '15px',\n                  textAlign: 'center',\n                  border: '1px solid #ddd',\n                  color: '#dc3545'\n                }}>\n                  {monthlyData.factoryExpenses > 0 ? formatCurrency(monthlyData.factoryExpenses) : '-'}\n                </td>\n                <td style={{\n                  padding: '15px',\n                  textAlign: 'center',\n                  border: '1px solid #ddd',\n                  color: '#dc3545'\n                }}>\n                  {monthlyData.livingExpenses > 0 ? formatCurrency(monthlyData.livingExpenses) : '-'}\n                </td>\n                <td style={{\n                  padding: '15px',\n                  textAlign: 'center',\n                  border: '1px solid #ddd',\n                  color: '#dc3545'\n                }}>\n                  {monthlyData.paintExpenses > 0 ? formatCurrency(monthlyData.paintExpenses) : '-'}\n                </td>\n                <td style={{\n                  padding: '15px',\n                  textAlign: 'center',\n                  border: '1px solid #ddd',\n                  color: '#dc3545'\n                }}>\n                  {monthlyData.transportExpenses > 0 ? formatCurrency(monthlyData.transportExpenses) : '-'}\n                </td>\n                <td style={{\n                  padding: '15px',\n                  textAlign: 'center',\n                  border: '1px solid #ddd',\n                  color: '#dc3545'\n                }}>\n                  {monthlyData.warehousePurchases > 0 ? formatCurrency(monthlyData.warehousePurchases) : '-'}\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n\n        {/* رصيد الخزينة */}\n        <div style={{\n          display: 'flex',\n          justifyContent: 'center',\n          marginTop: '20px'\n        }}>\n          <div style={{\n            display: 'flex',\n            alignItems: 'center'\n          }}>\n            <div style={{\n              backgroundColor: '#dc3545',\n              color: 'white',\n              padding: '15px 30px',\n              fontSize: '20px',\n              fontWeight: 'bold',\n              borderRadius: '5px 0 0 5px'\n            }}>\n              {formatCurrency(monthlyData.treasuryBalance)}\n            </div>\n            <div style={{\n              backgroundColor: '#1e3a8a',\n              color: 'white',\n              padding: '15px 30px',\n              fontSize: '20px',\n              fontWeight: 'bold',\n              borderRadius: '0 5px 5px 0'\n            }}>\n              الخزينة\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* الأقسام الفرعية المفصلة */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',\n        gap: '20px',\n        marginTop: '30px'\n      }}>\n\n        {/* مشتريات المخزن */}\n        <DetailedSection\n          title=\"مشتريات المخزن\"\n          total={monthlyData.warehousePurchases}\n          items={[]}\n        />\n\n        {/* مصروفات الحركة والنقل */}\n        <DetailedSection\n          title=\"مصروفات الحركة والنقل\"\n          total={monthlyData.transportExpenses}\n          items={[\n            { description: 'نقل مرون الحلاج', amount: 26.00, date: '06/01/2025' }\n          ]}\n        />\n\n        {/* مصروفات إبعائية */}\n        <DetailedSection\n          title=\"مصروفات إبعائية\"\n          total={monthlyData.livingExpenses}\n          items={[]}\n        />\n\n        {/* مصروفات الطلاء */}\n        <DetailedSection\n          title=\"مصروفات الطلاء\"\n          total={monthlyData.paintExpenses}\n          items={[]}\n        />\n\n        {/* مصروفات المصنع */}\n        <DetailedSection\n          title=\"مصروفات المصنع\"\n          total={monthlyData.factoryExpenses}\n          items={[]}\n        />\n\n        {/* مسحوبات الموظفين */}\n        <DetailedSection\n          title=\"مسحوبات الموظفين\"\n          total={monthlyData.employeeWithdrawals}\n          items={[]}\n        />\n\n        {/* إيداعات الخزينة */}\n        <DetailedSection\n          title=\"إيداعات الخزينة\"\n          total={monthlyData.treasuryDeposits}\n          items={[\n            { description: 'رصيد أول المدة', amount: 12180.5, date: '06/01/2025' },\n            { description: 'المهندس ناصر أثاث المقة', amount: 28000.0, date: '06/01/2025' }\n          ]}\n        />\n      </div>\n    </div>\n  );\n};\n\n// مكون القسم المفصل\nconst DetailedSection = ({ title, total, items }) => {\n  return (\n    <div className=\"card\">\n      <div style={{\n        backgroundColor: '#6c757d',\n        color: 'white',\n        padding: '10px',\n        textAlign: 'center',\n        fontWeight: 'bold',\n        fontSize: '16px'\n      }}>\n        {title}\n      </div>\n\n      <div style={{\n        backgroundColor: '#dc3545',\n        color: 'white',\n        padding: '10px',\n        textAlign: 'center',\n        fontWeight: 'bold',\n        fontSize: '18px'\n      }}>\n        {total > 0 ? formatCurrency(total) : '0'} إجمالي المصاريف\n      </div>\n\n      <table style={{\n        width: '100%',\n        borderCollapse: 'collapse',\n        fontSize: '14px'\n      }}>\n        <thead>\n          <tr style={{ backgroundColor: '#f8f9fa' }}>\n            <th style={{\n              padding: '10px',\n              textAlign: 'center',\n              border: '1px solid #ddd',\n              backgroundColor: '#ffa500',\n              color: 'white'\n            }}>\n              المبلغ\n            </th>\n            <th style={{\n              padding: '10px',\n              textAlign: 'center',\n              border: '1px solid #ddd'\n            }}>\n              الوصف\n            </th>\n            <th style={{\n              padding: '10px',\n              textAlign: 'center',\n              border: '1px solid #ddd'\n            }}>\n              التاريخ\n            </th>\n          </tr>\n        </thead>\n        <tbody>\n          {items.length > 0 ? items.map((item, index) => (\n            <tr key={index}>\n              <td style={{\n                padding: '8px',\n                textAlign: 'center',\n                border: '1px solid #ddd'\n              }}>\n                {formatCurrency(item.amount)}\n              </td>\n              <td style={{\n                padding: '8px',\n                textAlign: 'center',\n                border: '1px solid #ddd'\n              }}>\n                {item.description}\n              </td>\n              <td style={{\n                padding: '8px',\n                textAlign: 'center',\n                border: '1px solid #ddd'\n              }}>\n                {item.date}\n              </td>\n            </tr>\n          )) : (\n            // صفوف فارغة للعرض\n            Array.from({ length: 5 }, (_, index) => (\n              <tr key={index}>\n                <td style={{\n                  padding: '15px',\n                  textAlign: 'center',\n                  border: '1px solid #ddd'\n                }}></td>\n                <td style={{\n                  padding: '15px',\n                  textAlign: 'center',\n                  border: '1px solid #ddd'\n                }}></td>\n                <td style={{\n                  padding: '15px',\n                  textAlign: 'center',\n                  border: '1px solid #ddd'\n                }}></td>\n              </tr>\n            ))\n          )}\n        </tbody>\n      </table>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,mBAAmB;AAElD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,MAAMC,YAAY,GAAG,CAAC;;EAEtB;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC;IAC7CO,gBAAgB,EAAE,QAAQ;IAC1BC,mBAAmB,EAAE,OAAO;IAC5BC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,iBAAiB,EAAE,KAAK;IACxBC,kBAAkB,EAAE,CAAC;IACrBC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEFb,SAAS,CAAC,MAAM;IACd;IACA,MAAMc,OAAO,GAAGV,WAAW,CAACE,gBAAgB,IAC5BF,WAAW,CAACG,mBAAmB,GAC/BH,WAAW,CAACI,eAAe,GAC3BJ,WAAW,CAACK,cAAc,GAC1BL,WAAW,CAACM,aAAa,GACzBN,WAAW,CAACO,iBAAiB,GAC7BP,WAAW,CAACQ,kBAAkB,CAAC;IAE/CP,cAAc,CAACU,IAAI,KAAK;MACtB,GAAGA,IAAI;MACPF,eAAe,EAAEC;IACnB,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAIN,oBACEhB,KAAA,CAAAkB,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAExBzB,KAAA,CAAAkB,aAAA;IAAKC,SAAS,EAAC,MAAM;IAACO,KAAK,EAAE;MAAEC,YAAY,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAS,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzEzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTG,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBC,MAAM,EAAE;IACV,CAAE;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACCpB,YAAY,EAAC,mEACZ,CACD,CAAC,eAGNL,KAAA,CAAAkB,aAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBzB,KAAA,CAAAkB,aAAA;IAAKC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtCzB,KAAA,CAAAkB,aAAA;IAAOQ,KAAK,EAAE;MACZO,KAAK,EAAE,MAAM;MACbC,cAAc,EAAE,UAAU;MAC1BJ,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE;IACd,CAAE;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACAzB,KAAA,CAAAkB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MAAES,eAAe,EAAE,SAAS;MAAEN,KAAK,EAAE;IAAQ,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxDzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MAAEU,OAAO,EAAE,MAAM;MAAER,SAAS,EAAE,QAAQ;MAAES,MAAM,EAAE;IAAiB,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uFAAmB,CAAC,eACnGzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MAAEU,OAAO,EAAE,MAAM;MAAER,SAAS,EAAE,QAAQ;MAAES,MAAM,EAAE;IAAiB,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6FAAoB,CAAC,eACpGzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MAAEU,OAAO,EAAE,MAAM;MAAER,SAAS,EAAE,QAAQ;MAAES,MAAM,EAAE;IAAiB,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,iFAAkB,CAAC,eAClGzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MAAEU,OAAO,EAAE,MAAM;MAAER,SAAS,EAAE,QAAQ;MAAES,MAAM,EAAE;IAAiB,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uFAAmB,CAAC,eACnGzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MAAEU,OAAO,EAAE,MAAM;MAAER,SAAS,EAAE,QAAQ;MAAES,MAAM,EAAE;IAAiB,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,iFAAkB,CAAC,eAClGzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MAAEU,OAAO,EAAE,MAAM;MAAER,SAAS,EAAE,QAAQ;MAAES,MAAM,EAAE;IAAiB,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sHAAyB,CAAC,eACzGzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MAAEU,OAAO,EAAE,MAAM;MAAER,SAAS,EAAE,QAAQ;MAAES,MAAM,EAAE;IAAiB,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,iFAAkB,CAC/F,CACC,CAAC,eACRzB,KAAA,CAAAkB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MAAES,eAAe,EAAE;IAAU,CAAE;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxCzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE,gBAAgB;MACxBR,KAAK,EAAE,SAAS;MAChBE,UAAU,EAAE;IACd,CAAE;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACCtB,cAAc,CAACG,WAAW,CAACE,gBAAgB,CAC1C,CAAC,eACLR,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE,gBAAgB;MACxBR,KAAK,EAAE;IACT,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACCnB,WAAW,CAACG,mBAAmB,GAAG,CAAC,GAAGN,cAAc,CAACG,WAAW,CAACG,mBAAmB,CAAC,GAAG,GACvF,CAAC,eACLT,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE,gBAAgB;MACxBR,KAAK,EAAE;IACT,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACCnB,WAAW,CAACI,eAAe,GAAG,CAAC,GAAGP,cAAc,CAACG,WAAW,CAACI,eAAe,CAAC,GAAG,GAC/E,CAAC,eACLV,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE,gBAAgB;MACxBR,KAAK,EAAE;IACT,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACCnB,WAAW,CAACK,cAAc,GAAG,CAAC,GAAGR,cAAc,CAACG,WAAW,CAACK,cAAc,CAAC,GAAG,GAC7E,CAAC,eACLX,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE,gBAAgB;MACxBR,KAAK,EAAE;IACT,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACCnB,WAAW,CAACM,aAAa,GAAG,CAAC,GAAGT,cAAc,CAACG,WAAW,CAACM,aAAa,CAAC,GAAG,GAC3E,CAAC,eACLZ,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE,gBAAgB;MACxBR,KAAK,EAAE;IACT,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACCnB,WAAW,CAACO,iBAAiB,GAAG,CAAC,GAAGV,cAAc,CAACG,WAAW,CAACO,iBAAiB,CAAC,GAAG,GACnF,CAAC,eACLb,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE,gBAAgB;MACxBR,KAAK,EAAE;IACT,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACCnB,WAAW,CAACQ,kBAAkB,GAAG,CAAC,GAAGX,cAAc,CAACG,WAAW,CAACQ,kBAAkB,CAAC,GAAG,GACrF,CACF,CACC,CACF,CACJ,CAAC,eAGNd,KAAA,CAAAkB,aAAA;IAAKQ,KAAK,EAAE;MACVY,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,SAAS,EAAE;IACb,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACAzB,KAAA,CAAAkB,aAAA;IAAKQ,KAAK,EAAE;MACVY,OAAO,EAAE,MAAM;MACfG,UAAU,EAAE;IACd,CAAE;IAAArB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACAzB,KAAA,CAAAkB,aAAA;IAAKQ,KAAK,EAAE;MACVS,eAAe,EAAE,SAAS;MAC1BN,KAAK,EAAE,OAAO;MACdO,OAAO,EAAE,WAAW;MACpBN,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBW,YAAY,EAAE;IAChB,CAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACCtB,cAAc,CAACG,WAAW,CAACS,eAAe,CACxC,CAAC,eACNf,KAAA,CAAAkB,aAAA;IAAKQ,KAAK,EAAE;MACVS,eAAe,EAAE,SAAS;MAC1BN,KAAK,EAAE,OAAO;MACdO,OAAO,EAAE,WAAW;MACpBN,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBW,YAAY,EAAE;IAChB,CAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,4CAEE,CACF,CACF,CACF,CAAC,eAGNzB,KAAA,CAAAkB,aAAA;IAAKQ,KAAK,EAAE;MACVY,OAAO,EAAE,MAAM;MACfK,mBAAmB,EAAE,sCAAsC;MAC3DC,GAAG,EAAE,MAAM;MACXJ,SAAS,EAAE;IACb,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAGAzB,KAAA,CAAAkB,aAAA,CAAC2B,eAAe;IACdC,KAAK,EAAC,iFAAgB;IACtBC,KAAK,EAAEzC,WAAW,CAACQ,kBAAmB;IACtCkC,KAAK,EAAE,EAAG;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CAAC,eAGFzB,KAAA,CAAAkB,aAAA,CAAC2B,eAAe;IACdC,KAAK,EAAC,sHAAuB;IAC7BC,KAAK,EAAEzC,WAAW,CAACO,iBAAkB;IACrCmC,KAAK,EAAE,CACL;MAAEC,WAAW,EAAE,iBAAiB;MAAEC,MAAM,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAa,CAAC,CACrE;IAAA/B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACH,CAAC,eAGFzB,KAAA,CAAAkB,aAAA,CAAC2B,eAAe;IACdC,KAAK,EAAC,uFAAiB;IACvBC,KAAK,EAAEzC,WAAW,CAACK,cAAe;IAClCqC,KAAK,EAAE,EAAG;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CAAC,eAGFzB,KAAA,CAAAkB,aAAA,CAAC2B,eAAe;IACdC,KAAK,EAAC,iFAAgB;IACtBC,KAAK,EAAEzC,WAAW,CAACM,aAAc;IACjCoC,KAAK,EAAE,EAAG;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CAAC,eAGFzB,KAAA,CAAAkB,aAAA,CAAC2B,eAAe;IACdC,KAAK,EAAC,iFAAgB;IACtBC,KAAK,EAAEzC,WAAW,CAACI,eAAgB;IACnCsC,KAAK,EAAE,EAAG;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CAAC,eAGFzB,KAAA,CAAAkB,aAAA,CAAC2B,eAAe;IACdC,KAAK,EAAC,6FAAkB;IACxBC,KAAK,EAAEzC,WAAW,CAACG,mBAAoB;IACvCuC,KAAK,EAAE,EAAG;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CAAC,eAGFzB,KAAA,CAAAkB,aAAA,CAAC2B,eAAe;IACdC,KAAK,EAAC,uFAAiB;IACvBC,KAAK,EAAEzC,WAAW,CAACE,gBAAiB;IACpCwC,KAAK,EAAE,CACL;MAAEC,WAAW,EAAE,gBAAgB;MAAEC,MAAM,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAa,CAAC,EACtE;MAAEF,WAAW,EAAE,yBAAyB;MAAEC,MAAM,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAa,CAAC,CAC/E;IAAA/B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACH,CACE,CACF,CAAC;AAEV,CAAC;;AAED;AACA,MAAMoB,eAAe,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC;AAAM,CAAC,KAAK;EACnD,oBACEhD,KAAA,CAAAkB,aAAA;IAAKC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBzB,KAAA,CAAAkB,aAAA;IAAKQ,KAAK,EAAE;MACVS,eAAe,EAAE,SAAS;MAC1BN,KAAK,EAAE,OAAO;MACdO,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBG,UAAU,EAAE,MAAM;MAClBD,QAAQ,EAAE;IACZ,CAAE;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACCqB,KACE,CAAC,eAEN9C,KAAA,CAAAkB,aAAA;IAAKQ,KAAK,EAAE;MACVS,eAAe,EAAE,SAAS;MAC1BN,KAAK,EAAE,OAAO;MACdO,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBG,UAAU,EAAE,MAAM;MAClBD,QAAQ,EAAE;IACZ,CAAE;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACCsB,KAAK,GAAG,CAAC,GAAG5C,cAAc,CAAC4C,KAAK,CAAC,GAAG,GAAG,EAAC,wFACtC,CAAC,eAEN/C,KAAA,CAAAkB,aAAA;IAAOQ,KAAK,EAAE;MACZO,KAAK,EAAE,MAAM;MACbC,cAAc,EAAE,UAAU;MAC1BJ,QAAQ,EAAE;IACZ,CAAE;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACAzB,KAAA,CAAAkB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MAAES,eAAe,EAAE;IAAU,CAAE;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxCzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE,gBAAgB;MACxBF,eAAe,EAAE,SAAS;MAC1BN,KAAK,EAAE;IACT,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sCAEC,CAAC,eACLzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE;IACV,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAEC,CAAC,eACLzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE;IACV,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,4CAEC,CACF,CACC,CAAC,eACRzB,KAAA,CAAAkB,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGuB,KAAK,CAACI,MAAM,GAAG,CAAC,GAAGJ,KAAK,CAACK,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxCvD,KAAA,CAAAkB,aAAA;IAAIsC,GAAG,EAAED,KAAM;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACbzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,KAAK;MACdR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE;IACV,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACCtB,cAAc,CAACmD,IAAI,CAACJ,MAAM,CACzB,CAAC,eACLlD,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,KAAK;MACdR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE;IACV,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACC6B,IAAI,CAACL,WACJ,CAAC,eACLjD,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,KAAK;MACdR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE;IACV,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACC6B,IAAI,CAACH,IACJ,CACF,CACL,CAAC;EACA;EACAM,KAAK,CAACC,IAAI,CAAC;IAAEN,MAAM,EAAE;EAAE,CAAC,EAAE,CAACO,CAAC,EAAEJ,KAAK,kBACjCvD,KAAA,CAAAkB,aAAA;IAAIsC,GAAG,EAAED,KAAM;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACbzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE;IACV,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,eACRzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE;IACV,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,eACRzB,KAAA,CAAAkB,aAAA;IAAIQ,KAAK,EAAE;MACTU,OAAO,EAAE,MAAM;MACfR,SAAS,EAAE,QAAQ;MACnBS,MAAM,EAAE;IACV,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CACL,CACL,CAEE,CACF,CACJ,CAAC;AAEV,CAAC;AAED,eAAerB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}