import React, { useState, useEffect } from 'react';
import { formatCurrency, formatDate } from '../utils/currency';

const AccountStatements = () => {
  const [statements, setStatements] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    description: '',
    debit: '',
    credit: '',
    accountType: 'عام'
  });

  useEffect(() => {
    // جلب البيانات من قاعدة البيانات
    // مؤقتاً سنستخدم بيانات تجريبية
    setStatements([
      {
        id: 1,
        date: '2024-01-15',
        description: 'إيداع نقدي',
        debit: 0,
        credit: 25000,
        balance: 25000,
        accountType: 'خزينة'
      },
      {
        id: 2,
        date: '2024-01-14',
        description: 'مشتريات مواد خام',
        debit: 8500,
        credit: 0,
        balance: 16500,
        accountType: 'مخزن'
      },
      {
        id: 3,
        date: '2024-01-13',
        description: 'مصروفات نقل',
        debit: 3200,
        credit: 0,
        balance: 13300,
        accountType: 'نقل'
      }
    ]);
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const newStatement = {
      id: statements.length + 1,
      ...formData,
      debit: parseFloat(formData.debit) || 0,
      credit: parseFloat(formData.credit) || 0,
      balance: calculateNewBalance()
    };

    setStatements(prev => [newStatement, ...prev]);
    setFormData({
      date: new Date().toISOString().split('T')[0],
      description: '',
      debit: '',
      credit: '',
      accountType: 'عام'
    });
    setShowForm(false);
  };

  const calculateNewBalance = () => {
    const lastBalance = statements.length > 0 ? statements[0].balance : 0;
    const debit = parseFloat(formData.debit) || 0;
    const credit = parseFloat(formData.credit) || 0;
    return lastBalance + credit - debit;
  };



  const getTotalDebit = () => {
    return statements.reduce((sum, statement) => sum + statement.debit, 0);
  };

  const getTotalCredit = () => {
    return statements.reduce((sum, statement) => sum + statement.credit, 0);
  };

  const getCurrentBalance = () => {
    return getTotalCredit() - getTotalDebit();
  };

  return (
    <div className="account-statements">
      {/* ملخص الحساب */}
      <div className="stats-grid" style={{ marginBottom: '30px' }}>
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#dc3545' }}>
            {formatCurrency(getTotalDebit())}
          </div>
          <div className="stat-label">إجمالي المدين</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#ffa500' }}>
            {formatCurrency(getTotalCredit())}
          </div>
          <div className="stat-label">إجمالي الدائن</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#1e3a8a' }}>
            {formatCurrency(getCurrentBalance())}
          </div>
          <div className="stat-label">الرصيد الحالي</div>
        </div>
      </div>

      {/* كشف الحساب */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">كشف الحساب</h3>
          <button 
            className="btn btn-primary"
            onClick={() => setShowForm(!showForm)}
          >
            ➕ إضافة قيد جديد
          </button>
        </div>

        {/* نموذج إضافة قيد */}
        {showForm && (
          <form onSubmit={handleSubmit} style={{ marginBottom: '20px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
            <div className="form-row">
              <div className="form-group">
                <label className="form-label">التاريخ</label>
                <input
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  className="form-input"
                  required
                />
              </div>
              
              <div className="form-group">
                <label className="form-label">نوع الحساب</label>
                <select
                  name="accountType"
                  value={formData.accountType}
                  onChange={handleInputChange}
                  className="form-input"
                  required
                >
                  <option value="عام">عام</option>
                  <option value="خزينة">خزينة</option>
                  <option value="مخزن">مخزن</option>
                  <option value="نقل">نقل</option>
                  <option value="معيشة">معيشة</option>
                  <option value="طلاء">طلاء</option>
                  <option value="مصنع">مصنع</option>
                </select>
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">الوصف</label>
              <input
                type="text"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="form-input"
                placeholder="وصف المعاملة"
                required
              />
            </div>

            <div className="form-row">
              <div className="form-group">
                <label className="form-label">مدين</label>
                <input
                  type="number"
                  name="debit"
                  value={formData.debit}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="0.00"
                  step="0.01"
                />
              </div>
              
              <div className="form-group">
                <label className="form-label">دائن</label>
                <input
                  type="number"
                  name="credit"
                  value={formData.credit}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="0.00"
                  step="0.01"
                />
              </div>
            </div>

            <div style={{ display: 'flex', gap: '10px' }}>
              <button type="submit" className="btn btn-primary">
                💾 حفظ القيد
              </button>
              <button 
                type="button" 
                className="btn btn-secondary"
                onClick={() => setShowForm(false)}
              >
                ❌ إلغاء
              </button>
            </div>
          </form>
        )}

        {/* جدول كشف الحساب */}
        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>الوصف</th>
                <th>نوع الحساب</th>
                <th>مدين</th>
                <th>دائن</th>
                <th>الرصيد</th>
                <th>إجراءات</th>
              </tr>
            </thead>
            <tbody>
              {statements.map((statement) => (
                <tr key={statement.id}>
                  <td>{formatDate(statement.date)}</td>
                  <td>{statement.description}</td>
                  <td>
                    <span className="badge badge-info">{statement.accountType}</span>
                  </td>
                  <td style={{ color: statement.debit > 0 ? '#dc3545' : '#666' }}>
                    {statement.debit > 0 ? formatCurrency(statement.debit) : '-'}
                  </td>
                  <td style={{ color: statement.credit > 0 ? '#ffa500' : '#666' }}>
                    {statement.credit > 0 ? formatCurrency(statement.credit) : '-'}
                  </td>
                  <td style={{
                    fontWeight: 'bold',
                    color: statement.balance >= 0 ? '#1e3a8a' : '#dc3545'
                  }}>
                    {formatCurrency(statement.balance)}
                  </td>
                  <td>
                    <button className="btn btn-secondary" style={{ padding: '5px 10px', fontSize: '12px' }}>
                      ✏️ تعديل
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AccountStatements;
