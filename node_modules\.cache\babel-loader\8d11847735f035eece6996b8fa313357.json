{"ast": null, "code": "'use strict';\n\nconst ansiRegex = require('ansi-regex');\nmodule.exports = string => typeof string === 'string' ? string.replace(ansiRegex(), '') : string;", "map": {"version": 3, "names": ["ansiRegex", "require", "module", "exports", "string", "replace"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/node_modules/react-dev-utils/node_modules/strip-ansi/index.js"], "sourcesContent": ["'use strict';\nconst ansiRegex = require('ansi-regex');\n\nmodule.exports = string => typeof string === 'string' ? string.replace(ansiRegex(), '') : string;\n"], "mappings": "AAAA,YAAY;;AACZ,MAAMA,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC;AAEvCC,MAAM,CAACC,OAAO,GAAGC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,CAACC,OAAO,CAACL,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGI,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script"}