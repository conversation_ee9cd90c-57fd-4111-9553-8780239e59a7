import React, { useState } from 'react';
import { formatCurrency, formatDate } from '../utils/currency';

const LivingExpenses = () => {
  const [expenses, setExpenses] = useState([
    {
      id: 1,
      date: '2024-01-15',
      expenseType: 'إقامة فندقية',
      amount: 2500,
      beneficiary: 'أحمد محمد - موظف المبيعات',
      description: 'إقامة فندقية لمدة 3 أيام - مهمة عمل',
      receiptNumber: 'REC-001',
      location: 'فندق الكورنيش - طرابلس',
      duration: '3 أيام'
    },
    {
      id: 2,
      date: '2024-01-14',
      expenseType: 'وجبات طعام',
      amount: 800,
      beneficiary: 'فريق الصيانة',
      description: 'وجبات طعام أثناء العمل الميداني',
      receiptNumber: 'REC-002',
      location: 'مطعم النخيل',
      duration: 'يوم واحد'
    },
    {
      id: 3,
      date: '2024-01-13',
      expenseType: 'مواصلات',
      amount: 450,
      beneficiary: 'سالم عبدالله - مهندس',
      description: 'مواصلات للموقع والعودة',
      receiptNumber: 'REC-003',
      location: 'طرابلس - مصراتة',
      duration: 'يوم واحد'
    },
    {
      id: 4,
      date: '2024-01-12',
      expenseType: 'إقامة مؤقتة',
      amount: 1200,
      beneficiary: 'فريق التركيب',
      description: 'إقامة مؤقتة لفريق التركيب',
      receiptNumber: 'REC-004',
      location: 'شقق مفروشة - بنغازي',
      duration: '2 أيام'
    },
    {
      id: 5,
      date: '2024-01-11',
      expenseType: 'بدل سفر',
      amount: 600,
      beneficiary: 'محمد أحمد - مدير المشروع',
      description: 'بدل سفر ومصروفات شخصية',
      receiptNumber: 'REC-005',
      location: 'سبها',
      duration: 'يومين'
    }
  ]);



  const getTotalExpenses = () => {
    return expenses.reduce((sum, expense) => sum + expense.amount, 0);
  };

  const getExpensesByType = () => {
    const types = {};
    expenses.forEach(expense => {
      types[expense.expenseType] = (types[expense.expenseType] || 0) + expense.amount;
    });
    return types;
  };

  return (
    <div className="living-expenses">
      {/* إحصائيات مصروفات المعيشة */}
      <div className="stats-grid" style={{ marginBottom: '30px' }}>
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#dc3545' }}>
            {formatCurrency(getTotalExpenses())}
          </div>
          <div className="stat-label">إجمالي مصروفات المعيشة</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#1e3a8a' }}>
            {expenses.length}
          </div>
          <div className="stat-label">عدد المصروفات</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#ffa500' }}>
            {Object.keys(getExpensesByType()).length}
          </div>
          <div className="stat-label">أنواع المصروفات</div>
        </div>

        <div className="stat-card">
          <div className="stat-value" style={{ color: '#ea580c' }}>
            {formatCurrency(getTotalExpenses() / expenses.length)}
          </div>
          <div className="stat-label">متوسط المصروف</div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="card-title">مصروفات المعيشة</h3>
          <button className="btn btn-primary">
            ➕ إضافة مصروف معيشة
          </button>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>نوع المصروف</th>
                <th>المبلغ</th>
                <th>المستفيد</th>
                <th>المكان</th>
                <th>المدة</th>
                <th>الوصف</th>
                <th>رقم الإيصال</th>
                <th>إجراءات</th>
              </tr>
            </thead>
            <tbody>
              {expenses.map((expense) => (
                <tr key={expense.id}>
                  <td>{formatDate(expense.date)}</td>
                  <td>{expense.expenseType}</td>
                  <td style={{ fontWeight: 'bold', color: '#dc3545' }}>
                    {formatCurrency(expense.amount)}
                  </td>
                  <td>{expense.beneficiary}</td>
                  <td>{expense.location}</td>
                  <td>{expense.duration}</td>
                  <td>{expense.description}</td>
                  <td>{expense.receiptNumber}</td>
                  <td>
                    <button className="btn btn-secondary" style={{ padding: '5px 10px', fontSize: '12px' }}>
                      ✏️ تعديل
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default LivingExpenses;
