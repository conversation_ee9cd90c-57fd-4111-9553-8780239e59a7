{"ast": null, "code": "'use strict';\n\nmodule.exports = {\n  stdout: false,\n  stderr: false\n};", "map": {"version": 3, "names": ["module", "exports", "stdout", "stderr"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/node_modules/react-dev-utils/node_modules/supports-color/browser.js"], "sourcesContent": ["'use strict';\nmodule.exports = {\n\tstdout: false,\n\tstderr: false\n};\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,OAAO,GAAG;EAChBC,MAAM,EAAE,KAAK;EACbC,MAAM,EAAE;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}